<?php

namespace App\Modules\BankTransfer\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\BankTransfer\Requests\ShowListRequest;
use App\Modules\BankTransfer\Requests\StoreWireTransferRequest;
use App\Modules\BankTransfer\Services\BankTransferService;
use Inertia\Inertia;

class BankTransferController extends Controller
{
    public function index(ShowListRequest $request)
    {
        return Inertia::render('WireTransfer/WireTransferIndex', $request->show());
    }

    public function store(StoreWireTransferRequest $request)
    {
        BankTransferService::instance()->requestStore($request->all()); 

        return redirect()->route('account.balance.index');
    }
}
