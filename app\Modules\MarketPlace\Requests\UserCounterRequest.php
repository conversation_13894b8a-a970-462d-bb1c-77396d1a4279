<?php

namespace App\Modules\MarketPlace\Requests;

use App\Modules\MarketPlace\Services\MarketOfferService;
use Illuminate\Foundation\Http\FormRequest;

class UserCounterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer',
            'counter_offer_price' => 'required|integer|min:1',
            'feedback' => 'nullable|string|max:255'
        ];
    }

    public function counter() : void
    {
        MarketOfferService::instance()->counter($this->id, $this->counter_offer_price, $this->feedback);
    }
}
