<?php

namespace App\Console\Commands\Marketplace;

use App\Models\AfternicOffers;
use App\Models\Domain;
use App\Models\MarketPlaceDomains;
use App\Models\MarketPlacePaymentInvoice;
use App\Models\PaymentService;
use App\Models\RegisteredDomain;
use App\Modules\BankTransfer\Constants\BankTransferInvoiceStatusConstants;
use App\Modules\BankTransfer\Services\BankTransferService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\Payments\MarketInvoiceService;
use App\Modules\MarketPlace\Constants\MarketConstants;
use App\Modules\MarketPlace\Services\AfternicService; 
use App\Modules\MarketPlace\Services\AfternicReimbursementService; 

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MarketplaceBankTransferTransactionRejectedProcessCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:marketplace-bank-transfer-transaction-rejected-process-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $marketplaceInvoices = MarketPlacePaymentInvoice::query()
            ->where('status', '=', BankTransferInvoiceStatusConstants::BANK_TRANSFER_PAYMENT_REJECTED_PROCESSING)
            ->get();

        if ($marketplaceInvoices->isEmpty()) 
        {
            app(AuthLogger::class)->info("MarketplaceBankTransferTransactionRejectedProcessCommand : Nothing to process");

            return Command::SUCCESS;
        }
        else 
        {
            app(AuthLogger::class)->info("MarketplaceBankTransferTransactionRejectedProcessCommand : Processing {$marketplaceInvoices->count()} Entries");
        }

        foreach ($marketplaceInvoices as $marketplaceInvoice)
        {
            try 
            {
                $this->processInvoice($marketplaceInvoice);
            }
            catch(Exception $error)
            {
                app(AuthLogger::class)->error("MarketplaceBankTransferTransactionRejectedProcessCommand Error : {$error->getMessage()}");
            }
        }
    }

    /**
     * Process Invoices
     */
    private function processInvoice($marketplaceInvoice)
    {
        $paymentService    = PaymentService::find($marketplaceInvoice->payment_service_id);
        $userId            = $paymentService->user_id;
        $marketplaceOrders = MarketInvoiceService::instance()->getInvoiceData($marketplaceInvoice->id, $userId);

        $domainIds = [];  

        foreach($marketplaceOrders as $orders)
        {
            $domainIds[] = $orders->domain_id; 
        }

        //! GET THE DOMAINS CONNECTED FROM THIS MARKETPLACE INVOICE
        $registeredDomains = RegisteredDomain::query()
            ->select(
                'registered_domains.id as id',
                'registered_domains.user_contact_registrar_id as user_contact_registrar_id',
                'registered_domains.domain_id as domain_id', 
                'registered_domains.contacts_id as contact_id', 
                'registered_domains.extension_id as extension_id',
                'registered_domains.status as status',
                'registered_domains.locked_until as locked_until',
                'registered_domains.created_at as created_at',
                'registered_domains.updated_at as updated_at',
                'registered_domains.deleted_at as deleted_at',
                'market_place_domains.price as price',
                'market_place_domains.order_id as order_id',
                'market_place_domains.vendor as vendor',
                'market_place_domains.user_id as user_id', 
                'domains.name as name',
            )
            ->whereIn('domain_id', $domainIds)
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('market_place_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->get(); 

        app(AuthLogger::class)->info("MarketplaceBankTransferTransactionRejectedProcessCommand : Executing Marketplace Invoice #{$marketplaceInvoice->id}");

        DB::transaction(
            function() use ($registeredDomains, $marketplaceInvoice)
            {
                foreach ($registeredDomains as $registeredDomain)
                {
                    app(AuthLogger::class)->info("MarketplaceBankTransferTransactionRejectedProcessCommand : Executing Entry #{$marketplaceInvoice->id} Processing Domain {$registeredDomain->name} Vendor {$registeredDomain->vendor}");

                    switch($registeredDomain->vendor) 
                    {
                        case "afternic":
                            $offer = AfternicOffers::query()
                                ->where('domain_name', '=', $registeredDomain->name)
                                ->where('user_id', '=', $registeredDomain->user_id)
                                ->first();

                            $offerId = null;

                            if ($offer)
                            {
                                $offerId = $offer->id;  
                            }

                            BankTransferService::instance()->cancelAfternicOrder($registeredDomain, $offerId);

                            break;

                        default: 
                    }

                    //! RELEASE DOMAINS FROM HOLD
                    RegisteredDomain::query()
                        ->where('id', '=', $registeredDomain->domain_id)
                        ->update(['status' => MarketConstants::STATUS_CANCELLED]); 

                    MarketPlaceDomains::query()
                        ->where('registered_domain_id', '=',  $registeredDomain->domain_id)
                        ->where('user_id', '=', $registeredDomain->user_id)
                        ->update(['status' => MarketConstants::STATUS_CANCELLED]);

                    Domain::query()
                        ->where('id', '=', $registeredDomain->domain_id)
                        ->delete();

                    app(AuthLogger::class)->info("MarketplaceBankTransferTransactionRejectedProcessCommand : Domain {$registeredDomain->name} Successfully Cancelled");
                }
            
                $marketplaceInvoice->status = BankTransferInvoiceStatusConstants::BANK_TRANSFER_PAYMENT_REJECTED;
                $marketplaceInvoice->save();

                app(AuthLogger::class)->info("MarketplaceBankTransferTransactionRejectedProcessCommand : Marketplace Invoice #{$marketplaceInvoice->id} Done");     
            }
        ); 
    }  
}
