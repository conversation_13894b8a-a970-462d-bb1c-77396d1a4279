<?php

namespace App\Modules\Stripe\Helpers;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Stripe\Constants\StripeFees;
use App\Modules\Stripe\Constants\StripeFeeType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Providers\PaymentProvider;
use Illuminate\Support\Facades\Log;

class StripeFeeHelper
{
    use UserLoggerTrait;

    public static function calculateTransactionFee(float $goalAmount, string $type = StripeFeeType::INTERNATIONAL_CARD)
    {
        $percentFee = self::getPercentFee($type);
        $amountWithFee = $goalAmount + StripeFees::FIXED_FEE;

        $grossAmount = $amountWithFee / $percentFee;
        $serviceFee = $grossAmount - $goalAmount;

        return [
            'net_amount' => round($goalAmount, 2) ?? 0,
            'gross_amount' => round($grossAmount, 2) ?? 0,
            'service_fee' => round($serviceFee, 2) ?? 0,
        ];
    }

    public static function getStripeIntentById(string $paymentIntent)
    {
        $paymentIntentDetails = PaymentIntentProvider::instance()->retrieveIntent($paymentIntent);

        app(AuthLogger::class)->info('StripeFeeHelper@getStripeIntentById PaymentIntentDetails : ' . json_encode($paymentIntentDetails));

        $balance = self::getBalanceTransaction($paymentIntentDetails);

        if ($balance)
        {
            Log::info('Balance Transaction', [
                'id'     => $balance->id ?? null,
                'amount' => $balance->amount ?? null,
                'fee'    => $balance->fee ?? null,
                'net'    => $balance->net ?? null,
            ]);
    
            $grossAmount = self::convertFromStripeAmount($balance->amount ?? 0);
            $netAmount   = self::convertFromStripeAmount($balance->net ?? 0);
            $serviceFee  = self::convertFromStripeAmount($balance->fee ?? 0);
            $chargeId    = $balance->source ?? null;

            app(AuthLogger::class)->info('StripeFeeHelper@getStripeIntentById balance:: '.json_encode($balance));
        }

        return [
            'gross_amount'   => $grossAmount ?? null,
            'net_amount'     => $netAmount ?? null,
            'service_fee'    => $serviceFee ?? null,
            'charge_id'      => $chargeId ?? null,
            'payment_intent' => $paymentIntent,
        ];
    }

    public static function convertFromStripeAmount(int $amount)
    {
        return round($amount / 100, 2);
    }

    public static function getStripeAmount(float $amount)
    {
        return round($amount * 100, 2);
    }

    public static function getPercentFee(string $type)
    {
        switch ($type) {
            case StripeFeeType::INTERNATIONAL_CARD:
                return 1 - StripeFees::LOCAL_CARD_FEE - StripeFees::INTL_CARD_FEE;
            case StripeFeeType::INTL_CARD_CONVERSION:
                return 1 - StripeFees::LOCAL_CARD_FEE - StripeFees::INTL_CARD_FEE - StripeFees::CONVERSION_FEE;
            case StripeFeeType::LOCAL_CARD:
                return 1 - StripeFees::LOCAL_CARD_FEE;
            default:
                return 1 - StripeFees::LOCAL_CARD_FEE;
        }
    }

    private static function getBalanceTransaction(object $paymentIntentDetails)
    {
        Log::info("StripeFeeHelper@getBalanceTransaction PaymentIntent Details", (array) $paymentIntentDetails);

        $balance = $paymentIntentDetails->latest_charge->balance_transaction;

        if ($balance != null) 
        {
            app(AuthLogger::class)->info('StripeFeeHelper@getBalanceTransaction Balance Details : ' . json_encode($balance));

            return $balance;
        }

        $chargeId = $paymentIntentDetails->latest_charge->id;

        $chargeDetails = PaymentProvider::instance()->retrieveCharge($chargeId);

        app(AuthLogger::class)->info('StripeFeeHelper@getBalanceTransaction chargeDetails:: '.json_encode($chargeDetails));

        $balanceId = $chargeDetails->balance_transaction;

        if ($balanceId)
        {
            return PaymentProvider::instance()->retrieveBalanceTransaction($balanceId);
        }
        else 
        {
            return null;
        }
    }
}
