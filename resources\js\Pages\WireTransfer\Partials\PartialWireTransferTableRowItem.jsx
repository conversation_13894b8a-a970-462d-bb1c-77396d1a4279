//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdMoreVert} from "react-icons/md";
import { FaEye, FaRegThumbsUp, FaRegThumbsDown, FaRegHourglass, FaRegQuestionCircle } from 'react-icons/fa';

//* COMPONENTS
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";

//* PARTIALS
//... 

//* STATE
//...

//* HOOKS 
//... 

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import setDefaultDateFormat from '@/Util/setDefaultDateFormat';

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function PartialWireTransferTableRowItem(
    {
        item,
        isSelected,
        onCheckboxChange,
    }
)
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    useOutsideClick(ref, () => {
        setShow(false);
    });
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow] = useState(false);

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    const handleCheckboxChange = (e) => {
        onCheckboxChange(item.id, e);
    };

    const onHandleOnClick = (link, method, data) => {
        setShow(false);
        router.visit(link, { method: method, data: data });
    };

    const onHandleDelete = (domainCount, id) => {
        setShow(false);
        if (domainCount > 0) {
            router.post(route("category.warn"), { ids: [id] });
        }
        else {
            router.delete(
                route("category.delete", { ids: [id] }),
                { onSuccess: () => toast.success("Category has successfully been deleted.") }
            );
        }
    }

    const getStatus = () =>
    {
        let classNamesCommon   = "flex items-center gap-2 uppercase font-semibold"
        let classNamesSpecific = ""
        let icon               = <FaRegHourglass />;
        let status             = 'pending';

        if (item.deleted_at)
        {
            classNamesSpecific = 'text-danger';
            icon               = <FaRegThumbsDown />;
            status             = "rejected";
        }
        else if (item.verified_at)
        {
            classNamesSpecific = 'text-success';
            icon               = <FaRegThumbsUp />;
            status             = "verified";
        }
        else if (item.reviewed_at && !item.deleted_at)
        {
            classNamesSpecific = 'text-orange-500';
            icon               = <FaRegQuestionCircle />;
            status             = "unverified";
        }
        else if (!item.reviewed_at && !item.deleted_at)
        {
            classNamesSpecific = 'text-slate-500';
            icon               = <FaRegHourglass />;
            status             = "pending";
        }

        return (
            <div
                className={`
                    ${classNamesCommon} 
                    ${classNamesSpecific}     
                `}
            >
                <span>
                    {status}
                </span>
                {icon}
            </div>
        );
    };

    return (
        <tr className="hover:bg-gray-100">
            <td>
                <div className="flex items-center pl-2 space-x-2">
                    {/* <Checkbox
                        name="name"
                        value={item.name}
                        checked={isSelected}
                        handleChange={handleCheckboxChange}
                    /> */}
                    <div title={item.account_name}>
                        <span>{item.account_name}</span>
                    </div>
                </div>
            </td>
            <td>
                <span>{item.company}</span>
            </td>
            <td>
                <span>{parseFloat(item.amount).toFixed(2)}</span>
            </td>
            <td>
                <span
                    className='font-semibold text-primary uppercase'
                >
                    {item.purpose}
                </span>
            </td>
            <td>
                {getStatus()}
            </td>
            <td>
                <span>{setDefaultDateFormat(item.created_at) + ' ' + new Date(item.created_at + 'Z').toLocaleTimeString()}</span>
            </td>
            {/* <td>
                <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        <button
                            className="hover:bg-gray-100 px-5 py-1 flex justify-start"
                            onClick={() => onHandleOnClick(route("category.edit", { ids: [item.id] }), 'get')}
                        >
                            Edit
                        </button>
                        {item.is_default !== true && (
                            <>
                                <button
                                    className="hover:bg-gray-100 px-5 py-1 flex justify-start"
                                    onClick={() => onHandleOnClick(route("category.set.default", { id: [item.id] }), 'patch')}
                                >
                                    Set as Default
                                </button>
                                <button
                                    className="hover:bg-gray-100 px-5 py-1 flex justify-start"
                                    onClick={() => onHandleDelete(item.domain_count, item.id)}
                                >
                                    Delete
                                </button>
                            </>
                        )}
                    </DropDownContainer>
                </span>
            </td> */}
        </tr>
    );
}
