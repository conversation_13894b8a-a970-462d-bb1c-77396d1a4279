<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Connection Name
    |--------------------------------------------------------------------------
    |
    | Laravel's queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for every one. Here you may define a default connection.
    |
    */

    'default' => env('QUEUE_CONNECTION', 'sync'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    | Drivers: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'connections' =>
    [
        'domain_registration_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_registration_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_renewal_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_renewal_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_update_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_update_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_contacts_update_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_contacts_update_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_transfer_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_transfer_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_redemption_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_redemption_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_authcode_request_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_authcode_request_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_authcode_update_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_authcode_update_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'send_transfer_request_response_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'send_transfer_request_response_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'cancel_domain_transfer_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'cancel_domain_transfer_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'poll_update_domain_transfer_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'poll_update_domain_transfer_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'contact_registration_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'contact_registration_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'contact_update_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'contact_update_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'update_on_login_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'update_on_login_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'sync' =>
        [
            'driver' => 'sync',
        ],
        'mail_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'mail_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_exp_notif_sched_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_exp_notif_sched_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'payment_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'payment_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_refresh_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_refresh_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'otp_mail_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'otp_mail_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'user_domain_export_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'user_domain_export_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'authentication_attempts_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'authentication_attempts_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'stripe_identity_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'stripe_identity_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'market_place_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'market_place_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_classification_api_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_classification_api_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],
        'domain_delete_request_handler_jobs' =>
        [
            'driver'       => 'database',
            'table'        => 'domain_delete_request_handler_jobs',
            'queue'        => 'default',
            'retry_after'  => 120,
            'after_commit' => false,
        ],

        'database' =>
        [
            'driver'       => 'database',
            'table'        => 'jobs',
            'queue'        => 'default',
            'retry_after'  => 90,
            'after_commit' => false,
        ],

        'beanstalkd' =>
        [
            'driver'       => 'beanstalkd',
            'host'         => 'localhost',
            'queue'        => 'default',
            'retry_after'  => 90,
            'block_for'    => 0,
            'after_commit' => false,
        ],

        'sqs' =>
        [
            'driver'       => 'sqs',
            'key'          => env('AWS_ACCESS_KEY_ID'),
            'secret'       => env('AWS_SECRET_ACCESS_KEY'),
            'prefix'       => env('SQS_PREFIX', 'https://sqs.us-east-1.amazonaws.com/your-account-id'),
            'queue'        => env('SQS_QUEUE', 'default'),
            'suffix'       => env('SQS_SUFFIX'),
            'region'       => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'after_commit' => false,
        ],
        'redis' =>
        [
            'driver'       => 'redis',
            'connection'   => 'default',
            'queue'        => env('REDIS_QUEUE', 'default'),
            'retry_after'  => 90,
            'block_for'    => null,
            'after_commit' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Batching
    |--------------------------------------------------------------------------
    |
    | The following options configure the database and table that store job
    | batching information. These options can be updated to any database
    | connection and table which has been defined by your application.
    |
    */

    'batching' => [
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'job_batches',
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'driver' => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
    ],

];
