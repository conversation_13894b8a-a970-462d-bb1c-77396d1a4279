<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('auto_renewal_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('domain_id');
            $table->foreign('domain_id')->references('id')->on('domains');
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('id')->on('users');
            $table->enum('action', ['notification_sent', 'renewal_attempted', 'renewal_success', 'renewal_failed', 'grace_period_start']);
            $table->json('details')->nullable(); // Additional details about the action
            $table->timestamp('executed_at');
            $table->timestamps();

            $table->index(['domain_id', 'action', 'executed_at']);
            $table->index(['user_id', 'executed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('auto_renewal_logs');
    }
};
