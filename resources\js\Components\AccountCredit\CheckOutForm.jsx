import React, { useState, useEffect } from 'react';
import { useForm } from "@inertiajs/react";
import { toast } from "react-toastify";
import _PaymentSummary from '@/Constant/_PaymentSummary';

export default function CheckOutForm(
    {
        user,
        domains = [],
        market_domains = [],
        other_fees = {},
        type = 'register',
        availableCredit = 0,
        onCancel,
        onSuccess,
        isActive = false,
        onHandlePageProcessing = () => { },
    }) {
    const [error, setError] = useState(null);
    const [isProcessing, setProcessing] = useState(false);

    const getAmount = () => {
        if (!other_fees || typeof other_fees !== 'object') {
            console.error('Invalid other_fees object:', other_fees);
            return null;
        }

        if (!('bill_total' in other_fees)) {
            console.error('bill_total not found in other_fees:', other_fees);
            return null;
        }

        return other_fees.bill_total;
    };

    const amount = getAmount();

    useEffect(() => {
        if (amount === null) {
            setError("Unable to determine order amount. Please try again or contact support.");
        }
    }, [amount]);

    const { data, post } = useForm({
        payment_service_type: _PaymentSummary.SERVICE_TYPES.ACCOUNT_CREDIT,
        user_id: user?.id,
        domains: domains,
        market_domains: market_domains,
        other_fees: other_fees,
        amount_to_use: amount,
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        setProcessing(true);
        onHandlePageProcessing(true);

        let postRoute = null;

        switch (type) {
            case 'renew':
                postRoute = route("domain.renew");
                break;
            case 'transfer':
                postRoute = route("transfer.inbound.checkout");
                break;
            case 'multicheckout':
                postRoute = route("domain.multicart.checkout");
                break;
            // case 'redemption':
            //     postRoute = route("domain-redemption.restore");
            //     break;
            case 'redeem':
                postRoute = route("domain.redeem");
                break;
            case 'offercheckout':
                postRoute = route("marketoffer.checkout.store");
                break;
        }

        if (postRoute) {
            post(postRoute, {
                replace: true,
                preserveState: false,
                onSuccess: () => {
                    if (onSuccess) onSuccess();
                    setProcessing(false);
                    onHandlePageProcessing(false);
                },
                onError: (errors) => {
                    if (errors && errors.message) {
                        toast.error(errors.message, {
                            autoClose: 8000, // 8 seconds
                        });
                        setError(errors.message);
                    } else {
                        const errorMessage = "There was a problem processing your payment. Please try again.";
                        toast.error(errorMessage, {
                            autoClose: 8000, // 8 seconds
                        });
                        setError(errorMessage);
                    }
                    setProcessing(false);
                    onHandlePageProcessing(false);
                }
            }, {
                replace: true,
                preserveState: false,
            });
        }
    }

    const insufficientFunds = availableCredit < amount;

    return (
        <>
            {error && (
                <div className="mb-4 p-4 bg-red-50 text-red-600 rounded-md">
                    {error}
                </div>
            )}

            <div className={`bg-white rounded-lg border p-6 transition-all duration-200 ${isActive ? 'shadow border-primary' : 'border-gray-200'}`}>
                <div className="mb-2">
                    <h3 className="text-base text-gray-600">Account Balance</h3>
                </div>

                <div>
                    <span className="text-3xl font-semibold">
                        ${Number(availableCredit).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                    </span>
                </div>

                {insufficientFunds && amount !== null && (
                    <div className="mt-3 text-red-600 text-sm">
                        You don't have enough account credit to complete this purchase.
                    </div>
                )}

                {isActive && (
                    <form
                        data-payment-type="account_credit"
                        onSubmit={handleSubmit}
                    />
                )}
            </div>
        </>
    );
}
