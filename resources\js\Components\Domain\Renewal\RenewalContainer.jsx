//* PACKAGES 
import React, { useState, useEffect } from "react";
import { router, usePage, Link } from "@inertiajs/react";

//* ICONS
import { MdKeyboardBackspace } from "react-icons/md";

//* COMPONENTS
import AppVerificationPromptGroup from "@/Components/App/AppVerificationPromptGroupComponent";
import PrimaryButton from "@/Components/PrimaryButton";
import RenewItems from "./RenewItems";
import Checkbox from "@/Components/Checkbox";
import EmptyCart from "@/Components/Domain/Cart/EmptyCart";

//* PARTIALS
//...

//* STATE
//... 

//* UTILS 
import { getEventValue } from "@/Util/TargetInputEvent";
import UtilCheckIfHasSecuredTransaction from "@/Util/UtilCheckIfHasSecuredTransaction";

//* ENUMS
//... 

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function RenewalContainer(
    {
        data,
        settings
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! VARIABLES 
    const shouldVerifyUser = UtilCheckIfHasSecuredTransaction('domainRenewal');
    const comPrice = parseFloat(settings.renewal_fees.com.price);
    const netPrice = parseFloat(settings.renewal_fees.net.price);
    const orgPrice = parseFloat(settings.renewal_fees.org.price);

    const MAX_YEARS = 10;
    const YEAR_BY_SECS = 31557600;

    //! STATES
    const [stateShowVerificationPrompt, setStateShowVerificationPrompt] = useState(false);
    const [isAgree, setIsAgree] = useState(false);
    const [domainItems, setDomainItems] = useState(data);

    const initializeMaxRenewStatus = (data) => {
        var isStatus = false;

        data.map((domain, index) => {
            var isMax = checkMaxRenewExpiry(domain);
            if (isMax) isStatus = isMax;
        });

        return isStatus;
    }

    const getRenewalPrice = (tld) => {
        switch (parseInt(tld)) {
            case 1: return comPrice;
            case 2: return netPrice;
            case 3: return orgPrice;
            default: return 0;
        }
    }

    const initializeMaxItems = (data) => {
        let renewItems = [];
        data.map((domain, index) => renewItems[index] = checkMaxRenewExpiry(domain));
        return renewItems;
    }

    const checkMaxRenewStatus = (arrayItems) => {
        var isStatus = false;
        arrayItems.map((status, index) => { if (status) isStatus = status; });

        if (isMaxRenew) setIsAgree(false);
        setIsMaxRenew(isStatus);
    }

    const checkMaxRenewExpiry = (domain) => {
        var todayEpoch = Date.parse(new Date());
        var createdEpoch = Date.parse(domain.created_at);
        var addToCreatedYears = parseInt(new Date(domain.created_at).getFullYear()) + parseInt(MAX_YEARS);
        var expiredNumYears = Math.floor(((todayEpoch - createdEpoch) / 1000.0 / YEAR_BY_SECS));
        var addToMaxExpiryYear = addToCreatedYears + expiredNumYears;
        var createdMaxTermEpoch = new Date(domain.created_at).setFullYear(addToMaxExpiryYear);

        if (domain.expiry >= createdMaxTermEpoch) return true;
        return false;
    }

    const calculatePenalty = (domainExpiry) => {
        const currentDate = new Date();
        const expiryDate = new Date(domainExpiry);
        const penaltyStartDate = new Date(expiryDate);
        penaltyStartDate.setDate(penaltyStartDate.getDate() + settings.gracePeriod);

        return currentDate > penaltyStartDate ? settings.penalties.PENALTY_LATE_RENEWAL.value : 0;
    };

    const calculateRedemptionFee = (domainExpiry) => {
        const currentDate = new Date();
        const expiryDate = new Date(domainExpiry);
        const redemptionStartDate = new Date(expiryDate);
        redemptionStartDate.setDate(redemptionStartDate.getDate() + settings.redemptionPeriod);

        return currentDate >= redemptionStartDate ? settings.redemption_fee.value : 0;
    };


    const onRenewalTotal = (data) => {
        var total = 0;
        var price = 0;
        var penalty = 0;
        var redemptionFee = 0;

        data.map((domain) => {
            penalty = calculatePenalty(domain.expiry);
            redemptionFee = calculateRedemptionFee(domain.expiry);
            price = getRenewalPrice(domain.tld_id);
            total += domain.year_length * price + (+penalty) + (+redemptionFee);
        })

        return total;
    }

    const [isMaxRenew, setIsMaxRenew] = useState(initializeMaxRenewStatus(data));
    const [renewMaxItems, setRenewMaxItems] = useState(initializeMaxItems(data));
    const [renewalTotal, setRenewalTotal] = useState(onRenewalTotal(data))

    //! USE EFFECTS
    //... 

    //! FUNCTIONS
    function handleOnSubmit() {
        router.post(
            route("domain.renew.pay"),
            {
                user_id: user.id,
                domains: domainItems,
            }, {
            replace: true,
            preserveState: false,
        }
        );
    }

    const onHandleChangeAgree = (event) => {
        setIsAgree(getEventValue(event));
    };

    const getTotal = (renewalData) => {
        var rTotal = onRenewalTotal(renewalData);
        setRenewalTotal(rTotal);
    }

    const updateDomainItems = (column, value) => {
        const updated = domainItems.map(obj => {
            if (column == 'privacy') return { ...obj, privacy: value };
            if (column == 'auto_renew') return { ...obj, auto_renew: value };
        });

        setDomainItems(updated);
    }

    const onHandleSelectAllItem = (event) => {
        updateDomainItems(event.target.name, getEventValue(event));
    }

    // included ids for checkbox
    const changeSelected = (domains, value, event) => {
        let temp = [...domains];

        if (temp.includes(value)) temp = temp.filter((e) => e != value);
        else temp.push(value);
    };

    const onHandleChangeItem = (index, domain, column, value, status) => {
        let domains = [...domainItems]
        domains[index] = domain
        setDomainItems(domains);
        getTotal(domains);

        // disable if max registration
        let renewItems = [...renewMaxItems];
        renewItems[index] = status;
        setRenewMaxItems(renewItems);
        checkMaxRenewStatus(renewItems);
    }

    const onDeleteItem = (id, index) => {
        if (domainItems != null) {
            const temp = domainItems.filter((item) => item.id !== id);
            setDomainItems(temp);
            getTotal(temp);

            let renewItems = [...renewMaxItems];
            let removed = renewItems.splice(index, 1);
            setRenewMaxItems(renewItems);
            checkMaxRenewStatus(renewItems);
        }
    }

    if (domainItems.length == 0)
        return (
            <EmptyCart
                message={'No domains selected'}
                link={'domain'}
                isSearch={false}
            />
        );

    return (
        <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
            <AppVerificationPromptGroup
                isShow={stateShowVerificationPrompt}
                onClose={() => setStateShowVerificationPrompt(false)}
                onSubmitSuccess={handleOnSubmit}
                onSubmitError={() => alert('error')}
            />
            <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                <Link
                    href={route("domain")}
                >
                    <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                </Link>
                <span className="text-inherit">
                    Renewal Summary
                </span>
                <span className="text-gray-500">{domainItems.length} {(domainItems.length == 1) ? "item" : "items"}</span>
            </div>
            <div className="flex flex-col space-y-8 pt-8">
                {domainItems.map((domain, index) => {
                    return (
                        <RenewItems
                            key={"rl-" + domain.id}
                            domain={domain}
                            index={index}
                            handleChangeItem={onHandleChangeItem}
                            onDeleteItem={onDeleteItem}
                            renewalPrice={getRenewalPrice(domain.tld_id)}
                            gracePeriod={settings.gracePeriod}
                            penalty={calculatePenalty(domain.expiry)}
                            redemptionFee={calculateRedemptionFee(domain.expiry)}
                        />
                    );
                })}
                <div className="flex items-center justify-between space-y-2 text-lg text-gray-700 font-semibold">
                    <span className=" text-inherit">Renewal Total</span>
                    <span className=" text-inherit">${renewalTotal.toFixed(2)}</span>
                </div>
                <div className={
                    `flex items-center justify-end flex-col space-y-4 ${isMaxRenew && "opacity-0 pointer-events-none"}`
                }>
                    <label className="flex items-center">
                        <Checkbox
                            name="is_agree"
                            value="is_agree"
                            checked={isAgree}
                            handleChange={onHandleChangeAgree}
                            disabled={isMaxRenew}
                        />
                        <span className="ml-2 text-sm text-gray-600 text-center">
                            By clicking "Renew" you confirm that you have
                            read the StrangeDomains' &nbsp;
                            <a className="underline text-sm text-link" href={route("refund.policy")} target="_blank">
                                Refund Policy.
                            </a>
                        </span>

                    </label>
                    <PrimaryButton
                        className="w-full"
                        onClick={
                            (e) => {
                                e.preventDefault();

                                if (shouldVerifyUser == true) {
                                    setStateShowVerificationPrompt(true);
                                }
                                else {
                                    handleOnSubmit()
                                }
                            }
                        }
                        processing={!isAgree}
                    >
                        Renew
                    </PrimaryButton>
                </div>
            </div>
        </div>
    );
}
