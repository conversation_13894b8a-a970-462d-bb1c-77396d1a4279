<?php

namespace App\Modules\Cart\Requests;

use App\Events\MyCartCountEvent;
use App\Exceptions\FailedRequestException;
use App\Modules\BankTransfer\Constants\BankTransferPurposeConstants;
use App\Modules\Cart\Services\CheckoutCartService;
use App\Modules\Cart\Services\MultiCheckout\MultiCheckoutService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Payment\Constants\CheckoutType;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\TransactionThreshold\Services\TransactionThresholdService;
use App\Rules\Cart\CartArrayExists;
use App\Rules\Domain\DomainNotAvailable;
use App\Rules\MarketPlace\MarketCartArrayExists;
use App\Rules\Payment\ValidateOtherFees;
use App\Rules\Payment\ValidateStripeFees;
use App\Util\Constant\Transaction;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class MultiCheckoutRequest extends FormRequest
{
    public function rules(): array
    {
        return $this->getValidationRules();
    }

    public function prepareForValidation()
    {
        $this->merge(['payment_summary_type' => PaymentSummaryType::MULTI_CHECKOUT_INVOICE]);
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if (empty($this->domains) && empty($this->market_domains)) {
                $validator->errors()->add('', 'Cart is empty.');
            }

            $cartExistsRule = new CartArrayExists;
            $marketCartExistsRule = new MarketCartArrayExists;
            $domainNotAvailableRule = new DomainNotAvailable;

            $domainNames = $this->getDomainNames();

            if (! empty($this->domains) && ! $cartExistsRule->passes('domains', $this->domains['domains'])) {
                $validator->errors()->add('domains', $cartExistsRule->message());
            }

            if (! empty($this->market_domains) && ! $marketCartExistsRule->passes('market_domains', $this->market_domains['domains'])) {
                $validator->errors()->add('market_domains', $marketCartExistsRule->message());
            }

            if (! empty($domainNames) && ! $domainNotAvailableRule->passes('domains', $domainNames)) {
                $validator->errors()->add('domains', $domainNotAvailableRule->message($domainNames));
            }
        });
    }

    public function passedValidation()
    {
        if ($this->payment_service_type === PaymentServiceType::ACCOUNT_CREDIT) 
        {
            CheckoutCartService::instance()->checkAccountCreditBalance($this->amount_to_use);

            $this->cancelIntent();
        }

        if ($this->payment_service_type === PaymentServiceType::BANK_TRANSFER) 
        {
            CheckoutCartService::instance()->checkIfBankTransferAllowed($this->domains);

            $this->cancelIntent();
        }

        ///dd('Bank Transfer Success');

        MultiCheckoutService::instance()->checkMultiRegistryBalance($this->all());
        //TransactionThresholdService::instance()->validateTransaction(Transaction::REGISTER, $this->other_fees['domain_count'], $this->intent);

        $this->captureIntent();
    }

    protected function failedValidation(Validator $validator)
    {
        $this->cancelIntent();
        app(AuthLogger::class)->error(json_encode($validator->errors()));
        throw new FailedRequestException(404, $validator->errors()->first(), 'Page not found');
    }

    public function store(): string
    {
        app(AuthLogger::class)->info('multicheckout request store');

        $data = $this->all(); 

        if ($this->payment_service_type === PaymentServiceType::BANK_TRANSFER) 
        {
            $data['bankTransferPurpose'] = BankTransferPurposeConstants::MARKETPLACE_PAYMENT;
        }

        $invoiceId = MultiCheckoutService::instance()->store($data);
        
        MyCartCountEvent::dispatch(Auth::user()->id);

        return $invoiceId;
    }

    private function getValidationRules(): array
    {
        $rules = [
            'domains'              => ['array'],
            'market_domains'       => ['array'],
            'other_fees'           => ['required', 'array', 'min:1', new ValidateOtherFees(CheckoutType::MULTI_CHECKOUT)],
            'payment_service_type' => ['required', Rule::in([PaymentServiceType::STRIPE, PaymentServiceType::BANK_TRANSFER, PaymentServiceType::ACCOUNT_CREDIT])],
            'payment_summary_type' => ['required', Rule::in(PaymentSummaryType::ALL)],
        ];

        return match ($this->payment_service_type) 
        {
            PaymentServiceType::STRIPE => array_merge(
                $rules,
                [
                    'intent'      => ['required', 'string'],
                    'stripe_fees' => ['required', 'array', 'min:1', new ValidateStripeFees],
                ]
            ),
            PaymentServiceType::BANK_TRANSFER => array_merge(
                $rules, 
                [
                    'bankTransferName'    => ['required', 'string'],
                    'bankTransferCompany' => ['required', 'string']
                ]
            ),
            PaymentServiceType::ACCOUNT_CREDIT => array_merge(
                $rules, 
                [
                    'amount_to_use' => ['required', 'numeric']
                ]
            ),
            default => $rules
        };
    }

    private function cancelIntent()
    {
        if ($this->input('intent')) {
            PaymentIntentProvider::instance()->cancelIntent($this->input('intent'));
        }
    }

    private function captureIntent(): void
    {
        if (($this->payment_service_type === PaymentServiceType::STRIPE) &&
            $this->input('intent')
        ) {
            PaymentIntentProvider::instance()->captureIntent($this->input('intent'));
        }
    }

    private function getDomainNames()
    {
        $cartNames = [];
        $marketCartNames = [];

        if (! empty($this->domains['domains'])) {
            $cartObj = collect($this->domains['domains']);
            $cartNames = $cartObj->pluck('name')->toArray();
        }

        if (! empty($this->market_domains['domains'])) {
            $marketCartObj = collect($this->market_domains['domains']);
            $marketCartNames = $marketCartObj->pluck('name')->toArray();
        }

        return array_merge($cartNames, $marketCartNames);
    }
}
