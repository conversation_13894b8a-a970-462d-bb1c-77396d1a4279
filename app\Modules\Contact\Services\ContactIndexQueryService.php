<?php

namespace App\Modules\Contact\Services;

use App\Modules\Contact\Constants\ContactStatus;
use App\Modules\Contact\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use App\Util\Constant\Registry;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

class ContactIndexQueryService
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance()
    {
        $contactIndexQueryService = new self;

        return $contactIndexQueryService;
    }

    public function getData(ShowListRequest $request): array
    {
        $builder = $this->query();
        $this->pageLimit = $request->input('limit', 20);
        $this->whenHasEmail($builder, $request);
        $this->whenHasContact($builder, $request);
        $this->whenHasStatus($builder, $request);
        $this->whenHasRegistry($builder, $request);
        $this->whenHasOrderby($builder, $request);

        $builder = $builder->paginate($this->pageLimit)->withQueryString();

        return CursorPaginate::cursor($builder, $this->paramToURI($request));
    }

    // PRIVATE Functions

    private function query(): Builder
    {
        return DB::table('user_contacts')
            ->select('user_contacts.id as user_contact_id', 'user_contacts.*', 'contacts.*', 'registries.name as registry_name')
            ->join('contacts', 'contacts.id', '=', 'user_contacts.contact_id')
            ->join('registries', 'registries.id', '=', 'user_contacts.registry_id')
            ->where('user_contacts.user_id', auth()->user()->id);
    }

    private function whenHasEmail(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('contacts.email', 'like', $email.'%');
        });
    }

    private function whenHasContact(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('contact'), function (Builder $query) use ($request) {
            $contact = $request->contact;
            $query->whereRaw('LOWER(contacts.name) LIKE ?', [strtolower($contact).'%']);
        });
    }

    private function whenHasStatus(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('status'), function (Builder $query) use ($request) {
            $status = $request->status;
            if (! in_array($status, ['Registered','In Process'])) {
                return;
            }
            $query->where('contacts.status', $status);
        });
    }

    private function whenHasRegistry(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('registry'), function (Builder $query) use ($request) {
            $registry = $request->registry;
            if (! in_array($registry, Registry::ALL)) {
                return;
            }
            $query->where('registries.name', strtolower($registry));
        });
    }

    private function whenHasOrderby(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'created':
                        $query->orderBy('user_contacts.created_at', $orderby[1]);
                        break;
                    case 'name':
                        $query->orderBy('contacts.name', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('user_contacts.id', 'asc');
                }
            } else {
                $query->orderBy('user_contacts.id', 'desc');
            }
        })
            ->when(! $request->has('orderby'), function (Builder $query) {
                $query->orderBy('user_contacts.created_at', 'desc');
            });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('status')) {
            $param[] = 'status='.$request->status;
        }
        if ($request->has('registry')) {
            $param[] = 'registry='.$request->registry;
        }
        if ($request->has('orderby')) {
            $param[] = 'orderby='.$request->orderby;
        }
        if ($request->has('email')) {
            $param[] = 'email='.$request->email;
        }
        if ($request->has('contact')) {
            $param[] = 'contact='.$request->contact;
        }

        return $param;
    }
}
