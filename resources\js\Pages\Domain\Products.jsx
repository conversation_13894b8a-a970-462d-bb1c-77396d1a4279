import React, { useEffect, useState } from 'react'
import DataTable from 'react-data-table-component';
import UserLayout from "@/Layouts/UserLayout";
import ProductFilter from "@/Components/Domain/Product/ProductFilter";
import ProductStatusNav from "@/Components/Domain/Product/ProductStatusNav";
import ProductsList from "@/Components/Domain/Product/ProductsList";
import { router, useForm, usePage } from "@inertiajs/react";
import CursorPaginate from "@/Components/Util/CursorPaginate";
import DomainNotFound from "@/Components/Push/DomainNotFound";
import Product from "@/Constant/_Product";
import _Product from '@/Constant/_Product';


export default function Products({ items,
    status_type = "",
    onFirstPage,
    onLastPage,
    nextPageUrl,
    previousPageUrl,
    itemCount = 0,
    total = 0,
    itemName = "product", }) {

    items = items.map((item) => {
        return item.fees ? item : { ...item, 'fees': 0 };
    })

    const convertToTitleCase = (e) => {
        const str = e.toLowerCase();
        const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        return words.join(" ");
    }

    status_type = Object.values(status_type).map((e) => convertToTitleCase(e));
    
    const query = route().params;
    const limit = parseInt(query.limit) || 20;

    const handleLimitChange = (e) => {
        router.get(
            route("domains.products"),
            {
                ...route().params, // preserve existing query params
                limit: e.target.value,
                page: 1,
            },
            {
                preserveState: true,
                preserveScroll: true,
                replace: true,
            }
        );
    };

    const emptyListMessage = () => {
        let status = query.status ? query.status.toLowerCase() : ""
        switch (status) {
            case _Product.TAB_TYPES.pending:
                return "No pending products found.";
            case _Product.TAB_TYPES.transfer_requested:
                return "No tranfer requested products found.";
            case _Product.TAB_TYPES.completed:
                return "No completed products found.";
            case _Product.TAB_TYPES.cancelled:
                return "No cancelled products found.";
            default:
                return "No products found found.";
        }
    }


    useEffect(() => {
        if (document.querySelector('path[d="M7 10l5 5 5-5z"]')) document.querySelector('path[d="M7 10l5 5 5-5z"]').remove();
    }, []);

    return (
        <UserLayout >
            <div className="mx-auto container max-w-[1000px] mt-20 flex flex-col space-y-4">

                <div className="flex justify-start">
                    <label className="mr-2 text-sm pt-1 text-gray-600">
                        Show
                    </label>
                    <select
                        value={limit}
                        onChange={handleLimitChange}
                        className="border border-gray-300 rounded px-4 py-1 text-sm w-20"
                    >
                        {[20, 25, 30, 40, 50, 100].map((val) => (
                            <option key={val} value={val}>
                                {val}
                            </option>
                        ))}
                    </select>
                </div>

                <ProductFilter
                    status_type={status_type}
                    items={items}
                />

                <ProductStatusNav
                    status_type={status_type}
                    pageRoute={'domains.products'}
                />
                <div className="mt-1">
                    {items.length > 0 ?
                        (<ProductsList items={items} />)
                        :
                        (<DomainNotFound customMessage={emptyListMessage()} />)
                    }
                </div>

                <CursorPaginate
                    onFirstPage={onFirstPage}
                    onLastPage={onLastPage}
                    nextPageUrl={nextPageUrl}
                    previousPageUrl={previousPageUrl}
                    itemCount={itemCount}
                    total={total}
                    itemName={itemName}
                    shouldPreserveState={true}
                />
            </div>
        </UserLayout>
    );
}