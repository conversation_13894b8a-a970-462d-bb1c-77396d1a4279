<?php

namespace App\Listeners;

use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\DB;

class AddDefaultCategory
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $now = Carbon::now();

        DB::table('user_categories')->insert([
            'name' => 'Default',
            'user_id' => $event->user->id,
            'is_default' => true,
            'created_at' => $now,
            'updated_at' => $now,
        ]);
    }
}
