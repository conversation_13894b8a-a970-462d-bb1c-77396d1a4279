<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserContact extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'contact_id',
        'registry_id',
        'default_registrar_contact',
        'default_administrative_contact',
        'default_technical_contact',
        'default_billing_contact',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    public function registry()
    {
        return $this->belongsTo(Registry::class);
    }
}
