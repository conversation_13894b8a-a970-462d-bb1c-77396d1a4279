<?php

namespace App\Modules\Domain\Services;

use App\Models\Domain;
use App\Modules\Client\Constants\ScheduleType;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ExpirationNoticeService
{
    private static $maxTargetUserNotif = 50;

    private static $maxUserDomainList = 20;

    private static $maxPerCSV = 500;

    private static $csvName = 'expiry_domains_list';

    public static function getList(int $userId, string $latestExpiryDate, string $status = '', $isThreshold = true, $isLimit = true): Collection
    {
        $threshold = self::getDateThreshold($latestExpiryDate, $isThreshold);

        $payload = [
            'userId' => $userId,
            'isLimit' => $isLimit,
            'isThreshold' => $isThreshold,
            'minDate' => $threshold['min'],
            'maxDate' => $threshold['max'],
            'status' => empty($status) ? DomainStatus::ACTIVE : $status,
        ];

        return self::getUserDomain($payload);
    }

    public static function getUserExpiryNotification(): Collection
    {
        return DB::table('domain_expiration_notifications')
            ->join('users', 'users.id', '=', 'domain_expiration_notifications.user_id')
            ->where('expected_notification_at', '<', now())
            ->whereNotNull('record')
            ->whereJsonLength('record', '>', 0)
            ->select(
                'domain_expiration_notifications.id',
                'domain_expiration_notifications.user_id',
                'domain_expiration_notifications.expected_notification_at',
                'domain_expiration_notifications.lead_expiry_date',
                'domain_expiration_notifications.record',
                'domain_expiration_notifications.type',
                'users.email',
                'users.first_name',
                'users.last_name',
                'users.id as user_id'
            )->orderBy('domain_expiration_notifications.lead_expiry_date', 'asc')->limit(self::$maxTargetUserNotif)->get();
    }

    public static function setUserExpectationNotificationAt(array $ids, Carbon $datetime): void
    {
        DB::table('domain_expiration_notifications')
            ->whereIn('id', $ids)
            ->update(['expected_notification_at' => $datetime, 'updated_at' => now()]);
    }

    public static function createCSVFile(object $notice)
    {
        $timestamp = now()->timestamp;
        $filename = self::$csvName . '_' . $timestamp . '.csv';
        self::getCSVContent($notice, $filename);
        $filePath = Storage::disk('csv')->path($filename);

        return [
            'name' => $filename,
            'path' => $filePath,
        ];
    }
    private static function getDateThreshold(string $latestExpiryDate, bool $isThreshold): array
    {
        $defaultTime = now()->setTime(0, 0, 0);
        $expiryDate = empty($latestExpiryDate) ? (string) $defaultTime : $latestExpiryDate;
        $minDate = new Carbon($expiryDate);
        $maxDate = new Carbon($expiryDate);

        $minExpiryThreshold = $minDate->setTime(0, 0, 0)->timestamp * 1000;
        $maxExpiryThreshold = $maxDate->setTime(0, 0, 0)->addDays(ScheduleType::MAX_LAPSE_IN_DAYS)->timestamp * 1000;

        return [
            'min' => $minExpiryThreshold,
            'max' => $isThreshold ? $maxExpiryThreshold : 0,
        ];
    }

    private static function getUserDomain(array $payload): Collection
    {

        return self::getQuery($payload)->select('domains.id', 'domains.name', 'domains.expiry')->get();
    }

    private static function getQuery(array $payload): Builder
    {
        return Domain::join('registered_domains', 'registered_domains.domain_id', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', 'registered_domains.user_contact_registrar_id')
            ->when($payload['isThreshold'], function ($query) use ($payload) {
                return $query->where('domains.expiry', '<=', $payload['maxDate']);
            })
            ->where([
                ['domains.expiry', '>=', $payload['minDate']],
                ['domains.status', '=', $payload['status']],
                ['registered_domains.status', '=', UserDomainStatus::OWNED],
                ['user_contacts.user_id', '=', $payload['userId']],
                // Note: Auto-renewal field removed - all domains now treated as manual renewal
            ])
            ->orderBy('domains.expiry', 'asc')
            ->when($payload['isLimit'], function ($query) {
                return $query->limit(self::$maxUserDomainList);
            });
    }

    private static function getCSVContent(object $notice, string $filename)
    {
        $headings = self::getHeadings();
        $csvContent = self::convertArrayToCsvLine($headings);
        Storage::disk('csv')->put($filename, $csvContent);
        $threshold = self::getDateThreshold($notice->lead_expiry_date, true);

        $payload = [
            'userId' => $notice->user_id,
            'isLimit' => false,
            'isThreshold' => true,
            'minDate' => $threshold['min'],
            'maxDate' => $threshold['max'],
            'status' => DomainStatus::ACTIVE,
        ];

        try {
            self::getQuery($payload)
                ->select('domains.id', 'domains.name', 'domains.expiry')->chunk(
                    self::$maxPerCSV,
                    function ($domains) use ($filename) {
                        self::appendCSVFile($domains, $filename);
                    }
                );
        } catch (\Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
        }
    }

    private static function appendCSVFile(Collection $domains, string $filename): void
    {
        $csvContent = '';

        foreach ($domains as $domain) {
            $date = Carbon::createFromTimestampMs($domain->expiry)->format('Y/m/d');
            $csvContent .= self::convertArrayToCsvLine([$domain->name, $date]);
        }

        Storage::disk('csv')->append($filename, $csvContent);
    }

    private static function getHeadings()
    {
        return ['Name', 'Expiry Date'];
    }

    /**
     * Convert Array to CSV Line
     *
     * @param  array  $array
     */
    private static function convertArrayToCsvLine($array): string
    {
        $escapedValues = array_map(
            function ($value) {
                return '"' . str_replace('"', '""', $value) . '"';
            },
            $array
        );

        return implode(',', $escapedValues) . "\n";
    }
}
