<?php

namespace App\Modules\Contact\Requests;

use App\Modules\Contact\Constants\ContactType;
use App\Modules\Contact\Services\ContactService;
use App\Modules\TransactionThreshold\Services\TransactionThresholdService;
use App\Rules\ContactNameExists;
use App\Rules\ValidFormat;
use App\Util\Constant\Transaction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'registry' => ['required', 'array', Rule::in(['pir', 'verisign'])],
            'contact_name' => ['required', 'string', 'min:2', 'max:50', 'regex:/^[a-zA-Z\s]*$/', new ContactNameExists($this->registry)],
            'organization_name' => ['required', 'string', 'min:2', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:100', "regex:/^[0-9a-zA-Z.@_-]*$/"],
            'unit' => ['nullable', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'street' => ['required', 'string', 'max:50', "regex:/^[a-zA-Z0-9\s\-']*$/"],
            'city' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'state_province' => ['required', 'string', 'max:50', "regex:/^[\p{L}\s\-']+$/u"],
            'postal_code' => ['required', 'string', 'min:3', 'max: 8', "regex:/^[0-9a-zA-Z\s\-]*$/"],
            'country_code' => ['required', 'string', 'size:2', "regex:/^[a-zA-Z]*$/"],
            'ext_voice_number' => ['required', 'string', 'max:3', "regex:/[0-9]/"],
            'voice_number' => ['required', 'string', 'min:5', 'max: 12', 'regex:/[0-9]/'],
            'ext_fax_number' => ['nullable', 'string', 'max:3', 'regex:/[0-9]/'],
            'fax_number' => ['nullable', 'string', 'min:5', 'max: 12', 'regex:/[0-9]/'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.regex' => 'The name should only contain letters and spaces.',
            'state_province.required' => 'The state/province field is required.',
            'country_code.required' => 'The country field is required.',
        ];
    }

    public function attributes(): array
    {
        return [
            'voice_number' => 'phone number',
            'ext_fax_number' => 'area code',
            'ext_voice_number' => 'area code',
        ];
    }

    protected function passedValidation(): void
    {
        $this->merge([
            'country_code' => strtoupper($this->country_code),
            'voice_number' => '+' . $this->ext_voice_number . '.' . $this->voice_number,
            'state_province' => ucwords(strtolower($this->state_province)),
            'name' => $this->contact_name,
        ]);

        //! ONLY ADD FAX_NUMBER IF IT'S NOT NULL
        if (!is_null($this->fax_number)) $this->merge(['fax_number' => '+' . $this->ext_fax_number . '.' . $this->fax_number,]);
        else $this->merge(['fax_number' => null,]);

        unset($this['contact_name']);

        $count = count($this->registry);
        TransactionThresholdService::instance()->validateTransaction(Transaction::CONTACT_CREATE, $count);
    }

    public function store()
    {
        //dd($this->fax_number);

        $eppData = $this->extractEppData();
        $localData = $this->extractLocalData();
        $defaultContacts = $this->extractDefaultContacts();

        ContactService::instance()->store($eppData, $localData, $defaultContacts, $this->registry);
    }

    // PRIVATE Functions

    private function extractEppData(): array
    {
        $exclusions = array_merge(['unit', 'registry', 'ext_fax_number', 'ext_voice_number'], ContactType::DEFAULTS);

        return $this->except($exclusions);
    }

    private function extractLocalData(): array
    {
        $exclusions = array_merge(['registry'], ContactType::DEFAULTS);

        return $this->except($exclusions);
    }

    private function extractDefaultContacts(): array
    {
        $defaultContactInputs = $this->only(ContactType::DEFAULTS);

        return array_filter($defaultContactInputs, function ($value) {
            return $value === true;
        });
    }
}
