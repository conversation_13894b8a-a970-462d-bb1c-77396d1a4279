<?php

namespace App\Modules\Push\Requests;

use App\Exceptions\FailedRequestException;
use App\Modules\Push\Constants\PushDomainStatus;
use App\Modules\Push\Services\IncomingPushDomainService;
use App\Modules\Push\Services\OutgoingPushDomainService;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ShowPushListRequest extends FormRequest
{
    private $status;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'orderby' => [
                'string',
                'max:30',
                Rule::in([
                    'name:asc',
                    'name:desc',
                    'Domain: Asc',
                    'Domain: Desc',
                    'Push Date: Asc',
                    'Push Date: Desc',
                ])
            ],
            'domain' => ['string', 'min:1', 'max:30'],
            'tab' => ['string', 'max:30', Rule::in(PushDomainStatus::TABS)],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new FailedRequestException(404, 'Invalid Parameter.', 'Page not found');
    }

    protected function passedValidation(): void
    {
        $tabName = $this->tab ?? PushDomainStatus::PENDING_TAB;
        $this->status = $tabName === PushDomainStatus::PENDING_TAB ? PushDomainStatus::ACTIVE_STATUS : PushDomainStatus::HISTORY_STATUS;
    }

    public function incoming_get()
    {
        return IncomingPushDomainService::instance()->getUserIncoming($this, $this->status);
    }

    public function outgoing_get()
    {
        return OutgoingPushDomainService::instance()->getUserOutgoing($this, $this->status);
    }
}
