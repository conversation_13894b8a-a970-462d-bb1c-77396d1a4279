<?php

namespace App\Console\Commands\Marketplace;

use App\Models\AfternicOffers;
use App\Models\MarketPlacePaymentInvoice;
use App\Models\PaymentService;
use App\Models\RegisteredDomain;
use App\Modules\BankTransfer\Constants\BankTransferInvoiceStatusConstants;
use App\Modules\MarketPlace\Jobs\AfternicCreateOrder;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\MarketPlace\Services\Payments\MarketInvoiceService; 
use Exception;

use Illuminate\Support\Facades\DB; 
use Illuminate\Console\Command;

class MarketplaceBankTransferTransactionApprovedProcessCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:marketplace-bank-transfer-transaction-approved-process-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process Bank Transfer Approved Transactions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $marketplaceInvoices = MarketPlacePaymentInvoice::query()
            ->where('status', '=', BankTransferInvoiceStatusConstants::BANK_TRANSFER_PAYMENT_APPROVED_PROCESSING)
            ->get();

        if ($marketplaceInvoices->isEmpty()) 
        {
            app(AuthLogger::class)->info("MarketplaceBankTransferTransactionApprovedProcessCommand : Nothing to process");

            return Command::SUCCESS;
        }
    
        
        foreach ($marketplaceInvoices as $marketplaceInvoice)
        {
            try 
            {
                $this->processInvoice($marketplaceInvoice);
            }
            catch(Exception $error)
            {
                app(AuthLogger::class)->error("MarketplaceBankTransferTransactionApprovedProcessCommand Error : {$error->getMessage()}");
            }
        }
    }

    /**
     * Process Invoices
     */
    private function processInvoice($marketplaceInvoice)
    {
        $paymentService = PaymentService::find($marketplaceInvoice->payment_service_id);
    
        $userId = $paymentService->user_id;

        $marketplaceOrders =  MarketInvoiceService::instance()->getInvoiceData($marketplaceInvoice->id, $userId);

        $domainIds = [];  

        foreach($marketplaceOrders as $orders)
        {
            $domainIds[] = $orders->domain_id; 
        }

        //! GET THE DOMAINS CONNECTED FROM THIS MARKETPLACE INVOICE
        $registeredDomains = RegisteredDomain::query()
            ->select(
                'registered_domains.id as id',
                'registered_domains.user_contact_registrar_id as user_contact_registrar_id',
                'registered_domains.domain_id as domain_id', 
                'registered_domains.contacts_id as contact_id', 
                'registered_domains.extension_id as extension_id',
                'registered_domains.status as status',
                'registered_domains.locked_until as locked_until',
                'registered_domains.created_at as created_at',
                'registered_domains.updated_at as updated_at',
                'registered_domains.deleted_at as deleted_at',
                'market_place_domains.price as price',
                'market_place_domains.order_id as order_id',
                'market_place_domains.vendor as vendor',
                'market_place_domains.user_id as user_id',
                'domains.name as name',
            )
            ->whereIn('domain_id', $domainIds)
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('market_place_domains', 'market_place_domains.registered_domain_id', '=', 'registered_domains.id')
            ->get(); 

        app(AuthLogger::class)->info("MarketplaceBankTransferTransactionApprovedProcessCommand Executing Entry #{$marketplaceInvoice->id}");

        DB::transaction(
            function() use ($registeredDomains, $marketplaceInvoice, $userId)
            {
                foreach ($registeredDomains as $registeredDomain)
                {
                    $from = "market";

                    app(AuthLogger::class)->info("MarketplaceBankTransferTransactionApprovedProcessCommand Executing Entry #{$marketplaceInvoice->id} Processing Domain {$registeredDomain->name} Vendor {$registeredDomain->vendor}");

                    switch($registeredDomain->vendor) 
                    {
                        case "afternic":
                            $offer = AfternicOffers::query()
                                ->where('domain_name', '=', $registeredDomain->name)
                                ->where('user_id', '=', $registeredDomain->user_id)
                                ->first();

                            $offerId = null;

                            if ($offer)
                            {
                                $from    = 'offer'; 
                                $offerId = $offer->id;  
                            }

                            $offerId = $offer ? $offer->id : null;

                            AfternicCreateOrder::dispatch(
                                $registeredDomain, 
                                $userId, 
                                $from,  
                                $offerId
                            );
                            break; 

                        default: 
                    }
                }
                
                $marketplaceInvoice->status = BankTransferInvoiceStatusConstants::BANK_TRANSFER_PAYMENT_PAID;
                $marketplaceInvoice->save(); 
            }
        );

    }
}
