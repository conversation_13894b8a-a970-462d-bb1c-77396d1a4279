<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->boolean('is_renewal')->default(true)->after('is_refunded');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domain_cancellation_requests', function (Blueprint $table) {
            $table->dropColumn('is_renewal');
        });
    }
};
