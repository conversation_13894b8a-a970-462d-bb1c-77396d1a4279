<?php

namespace App\Modules\Domain\Requests;

use App\Exceptions\UserDomainException;
use App\Modules\Domain\Services\DomainService;
use App\Modules\TransactionThreshold\Services\TransactionThresholdService;
use App\Util\Constant\Transaction;
use App\Util\Helper\Domain\DomainTld;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class DomainAuthCodeRequest extends FormRequest
{
    private $maxDomainsPerEmail = 500;

    private $regenerationThresholdInHours = 24; // domain auth code is not allowed to regenerate within 24 hours.

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'domains' => ['required'],
        ];
    }

    protected function passedValidation()
    {
        $count = count($this->domains);

        TransactionThresholdService::instance()->validateTransaction(Transaction::AUTH_REQUEST, $count);

        $this->validateDomainExpiry();
    }

    public function validateDomainExpiry()
    {
        $now = Carbon::now();
        $fortyFourDaysAgo = $now->copy()->subDays(44);
        $fortyFourDaysAgoTimestamp = $fortyFourDaysAgo->getTimestampMs();

        foreach ($this->domains as $domain) {
            if (isset($domain['expiry']) && $domain['expiry'] < $fortyFourDaysAgoTimestamp) {
                throw new UserDomainException(
                    400,
                    'Authentication code requests are not allowed for domains that are 44 or more days expired.',
                    'Domain Expired'
                );
            }
        }
    }

    public function sendAuthCode()
    {
        $registry_extensions = DomainTld::getRegistryExtensions();
        $now = Carbon::now();
        $registry_domains = [];
        $domains_for_update = [];

        foreach ($registry_extensions as $registry => $extensions) {
            $processedDomains = array_map(function ($domain) use ($now, $registry, &$domains_for_update) {
                $last_update = Carbon::parse($domain['auth_code_updated_at']);

                if ($last_update->diffInSeconds($now) > ($this->regenerationThresholdInHours * 60 * 60)) {
                    $domains_for_update[] = ['name' => $domain['name'], 'registry' => $registry];
                }

                return ['name' => $domain['name']];
            }, array_values(array_filter($this->domains, function ($domain) use ($extensions) {
                return in_array($domain['extension_name'], $extensions);
            })));

            $registry_domains[$registry] = array_chunk($processedDomains, $this->maxDomainsPerEmail);
        }

        DomainService::instance()->sendAuthCode($registry_domains, $domains_for_update, $this->maxDomainsPerEmail);
    }
}
