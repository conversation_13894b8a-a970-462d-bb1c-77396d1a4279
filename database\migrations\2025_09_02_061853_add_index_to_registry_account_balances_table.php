<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('registry_account_balances', function (Blueprint $table) {
            // Add composite index for registry_id and id to optimize the getBalances query
            $table->index(['registry_id', 'id'], 'idx_registry_account_balances_registry_id_id');
            
            // Add index for registry_id alone for better general performance
            $table->index('registry_id', 'idx_registry_account_balances_registry_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('registry_account_balances', function (Blueprint $table) {
            $table->dropIndex('idx_registry_account_balances_registry_id_id');
            $table->dropIndex('idx_registry_account_balances_registry_id');
        });
    }
};