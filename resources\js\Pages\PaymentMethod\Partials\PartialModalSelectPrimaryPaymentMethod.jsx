//* PACKAGES
import React, { useEffect, useState } from 'react';

//* ICONS
import { IoMdCloseCircle } from "react-icons/io";

//* COMPONENTS
import Modal from '@/Components/Modal';
import PrimaryButton from '@/Components/PrimaryButton'

import { useForm } from '@inertiajs/react';
import { toast } from 'react-toastify';

//* PARTIALS


//* STATE
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...
export default function PartialModalSelectPrimaryPaymentMethod(
    {
        //! VARIABLES
        primaryPaymentMethodRetrieved,

        //! STATES 
        isModalOpen = false,

        //! EVENTS
        handleModalClose,

    }
) {
    //! PACKAGE

    //! VARIABLES
    const form = useForm({
        primary_payment_method: '',
    })
    //...

    //! STATES
    const [primaryPaymentMethod, setPrimaryPaymentMethod] = useState('');

    //...

    //! USE EFFECTS
    useEffect(() => {
        setPrimaryPaymentMethod(primaryPaymentMethodRetrieved);
    }, [])

    useEffect(() => {
        form.setData('primary_payment_method', primaryPaymentMethod);
    }, [primaryPaymentMethod])

    //... 


    //! FUNCTIONS   
    function handleSelfClose() {
        //* CLOSE MODAL
        handleModalClose();
    }

    const setRadioButton = (e) => {
        setPrimaryPaymentMethod(e.target.value);
    }

    const submitPrimaryPaymentMethod = (e) => {
        form.patch(route('primary-payment-method.store'),
            {
                onStart: () => {
                    toast.info('Updating primary payment method.', { autoClose: 3000 })
                    handleModalClose();
                },
                onSuccess: () => {
                    toast.info('Primary payment method updated.', { autoClose: 3000 });
                },
                onError: () => {
                    toast.info('Something went wrong.', { autoClose: 2000 })
                }
            });
    }



    return (
        <Modal
            show={isModalOpen}
            onClose={handleSelfClose}
            closeable={false}
            maxWidth='2xl'
        >
            <div
                className={`
                    flex flex-col justify-around
                    px-10 pt-5 pb-5
                    gap-y-5
                `}
            >
                {/* SECTION HEADER  */}
                <section
                    className='flex justify-between items-center gap-10 text-primary font-semibold text-lg'
                >
                    <div>
                        Add Primary Payment Method
                    </div>
                    <IoMdCloseCircle
                        onClick={() => handleSelfClose()}
                        className='text-primary ease-in-out duration-100 hover:text-blue-900 h-8 w-8 cursor-pointer'
                    />
                </section>

                <section className='space-y-2'>
                    <div className='border rounded-lg min-h-14 text-xl font-semibold flex items-center border-gray-700 shadow-xl'>
                        <input type='radio' className='ml-2' name='paymentMethod' value='saved_card' onChange={setRadioButton} checked={primaryPaymentMethod == 'saved_card'} />
                        <span className='pl-4'>Saved Cards</span>
                    </div>
                    <div className='border rounded-lg min-h-14 text-xl font-semibold flex items-center  border-gray-700 shadow-xl'>
                        <input type='radio' className='ml-2' name='paymentMethod' value='account_credit' onChange={setRadioButton} checked={primaryPaymentMethod == 'account_credit'} />
                        <span className='pl-4'>Account Credits</span>
                    </div>
                </section>
                <PrimaryButton
                    onClick={submitPrimaryPaymentMethod}
                >
                    Submit
                </PrimaryButton>
            </div>
        </Modal>
    );
}