<?php

namespace App\Console\Commands\Domain;

use App\Console\Commands\Constants\SchedulerTypes;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Services\UpdateServices\DeleteRequestHandlerService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class DeleteRequestHandler extends Command
{
    private $maxDelay = 6; // in hours
    const ACTION = ['REJECT' => 'reject', 'CANCEL' => 'cancel'];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'domain:delete-request-handler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '<PERSON><PERSON> delete requests to ensure domains are deleted before the grace period.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DeleteRequestHandler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('DeleteRequestHandler: Checking for domains...');

        $skip = $this->validateCronHistory();

        if ($skip) return;

        $domains = DeleteRequestHandlerService::instance()->getExpiredDeleteRequests();
        $hasData = $domains['toDelete']->isNotEmpty() || $domains['toReject']->isNotEmpty();
        $count = $domains['toDelete']->count() + $domains['toReject']->count();

        if ($domains['toDelete']->isNotEmpty()) {
            app(AuthLogger::class)->info('DeleteRequestHandler: Found ' . $count . ' domains to delete...');
            DeleteRequestHandlerService::instance()->jobDispatch($domains['toDelete'], self::ACTION['CANCEL']);
        }

        if ($domains['toReject']->isNotEmpty()) {
            app(AuthLogger::class)->info('DeleteRequestHandler: Found ' . $count . ' domains to reject...');
            DeleteRequestHandlerService::instance()->jobDispatch($domains['toReject'], self::ACTION['REJECT']);
        }

        if (!$hasData) app(AuthLogger::class)->info('DeleteRequestHandler: No domains found...');

        $this->updateCronHistory($hasData);
        app(AuthLogger::class)->info('DeleteRequestHandler: Done');
    }

    /**
     * Get all domains that have been expired for 40–45 days.
     */

    private function getCron(): object
    {
        $cron = DB::table('cron_run_histories')->where('name', SchedulerTypes::DELETE_REQUEST_HANDLER)->first();

        if ($cron) return $cron;

        $errorMsg = 'DeleteRequestHandler: No cron data found';
        app(AuthLogger::class)->error($errorMsg);
        throw new Exception($errorMsg);
    }

    private function validateCronHistory(): bool
    {
        $cron = $this->getCron();
        $lastRunAt = Carbon::parse($cron->last_run_at);
        $shouldRun = $cron->has_data || $lastRunAt->lessThanOrEqualTo(Carbon::now()->subHours($this->maxDelay));

        if ($shouldRun) return false;

        $errorMsg = 'DeleteRequestHandler: Skipping for ' . $this->maxDelay . ' hours...';
        app(AuthLogger::class)->info($errorMsg);

        return true;
    }

    private function updateCronHistory(bool $hasData): void
    {
        DB::table('cron_run_histories')
            ->where('name', SchedulerTypes::DELETE_REQUEST_HANDLER)
            ->update(['has_data' => $hasData, 'last_run_at' => Carbon::now()]);
    }
}
