<?php

namespace App\Modules\Cart\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Cart\Requests\AddToCartRequest;
use App\Modules\Cart\Requests\DeleteCartItemRequest;
use App\Modules\Cart\Requests\UpdateCartItemRequest;
use App\Modules\Cart\Services\CartService;
use App\Modules\Cart\Services\UserCart;
use App\Modules\MarketPlace\Requests\CartAddRequest;
use App\Modules\MarketPlace\Requests\CartRemoveRequest;
use App\Modules\MarketPlace\Services\MarketCartService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\ExtensionFees;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response as HttpResponse;
use Inertia\Inertia;
use Inertia\Response;

class CartController extends Controller
{
    public function myCart(): Response
    {
        $reg = CartService::instance()->getViewCart();
        $market = MarketCartService::instance()->getAll();
        $renewal = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::TRANSFER);

        $cart = new UserCart;
        $marketCount = MarketCartService::instance()->getCartCount();
        $totalCartCount = $marketCount + $cart->getTotalDomain();

        return Inertia::render(
            'Domain/Cart',
            ['cart' => $reg, 'market' => $market, 'renew' => $renewal, 'cartCount' => $totalCartCount]
        );
    }

    public function update(UpdateCartItemRequest $request): RedirectResponse
    {
        $request->update();

        return back();
    }

    public function delete(DeleteCartItemRequest $request): RedirectResponse
    {
        $request->delete();

        return back();
    }

    public function store(AddToCartRequest $request): HttpResponse
    {
        return $request->store();

        // return response('stored', 201);
    }

    public function storeAICart(CartAddRequest $request): void
    {
        // return;
        $request->add();
    }

    public function getCartCount(): HttpResponse
    {
        $cart = new UserCart;
        $market = MarketCartService::instance()->getCartCount();

        $count = $market + $cart->getTotalDomain();

        return response($count, 200);
    }

    public function removeMarketCart(CartRemoveRequest $request): RedirectResponse
    {
        $request->remove();

        return back();
    }
}
