<?php

namespace App\Modules\TransactionThreshold\Services;

use App\Exceptions\TransactionThresholdException;
use App\Modules\AdminNotification\Services\TransactionThresholdNotificationService;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\TransactionThreshold\Constants\TransactionThresholdRoutes;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use stdClass;

class TransactionThresholdService
{
    use UserLoggerTrait;

    private ?Carbon $now = null;
    private ?stdClass $userTransaction = null;
    private ?string $intent = null;
    private array $userObserverUpdatePayload = [];
    private array $systemObserverUpdatePayload = [];
    private bool $sendErrorPage = false;

    public static function instance()
    {
        $transactionThresholdService = new self;

        return $transactionThresholdService;
    }

    public function validateTransaction(string $transactionType, int $counter, ?string $intent = null)
    {
        $this->now = Carbon::now();
        $this->intent = $intent;
        $this->fetchUserTransactionDetails(Auth::user()->id, $transactionType);

        if (!$this->userTransaction) {
            throw new TransactionThresholdException(
                404,
                'Invalid Transaction.',
                'Page not found',
                route(TransactionThresholdRoutes::ROUTES[$transactionType]['exception']),
                TransactionThresholdRoutes::ROUTES[$transactionType]['label']
            );
        }

        $this->assertObservers();
        $this->validateTransactionCounter($counter);
        $this->upsertUserDailyTransactions($counter);
        $this->updateObservers();
        $this->handleTransactionReject();
    }

    // PRIVATE Functions

    private function fetchUserTransactionDetails(string $userId, string $transactionType): void
    {
        $this->userTransaction = DB::table('user_transactions')
            ->select(
                'user_transactions.id',
                'user_transactions.user_id',
                'user_transactions.transaction_id',
                'system_transaction_observers.id as system_observer_id',
                'user_transaction_observers.id as user_observer_id',
                'user_daily_transactions.id as user_daily_transaction_id',
                'system_daily_transactions.id as system_daily_transaction_id',
                'transaction_triggers.id as transaction_trigger_id',
                'transactions.name',
                'transactions.system_limit',
                'transactions.user_limit',
                'user_transactions.custom_limit',
                'system_transaction_observers.past_counter as system_past_counter',
                'user_transaction_observers.past_counter as user_past_counter',
                'user_daily_transactions.counter as user_daily_counter',
                'system_daily_transactions.counter as system_daily_counter',
                'transactions.length',
                'system_transaction_observers.start_at',
                'system_transaction_observers.end_at',
                'transaction_triggers.notify_subscriber',
                'transaction_triggers.allow_action'
            )
            ->join('transactions', 'transactions.id', '=', 'user_transactions.transaction_id')
            ->leftJoin('transaction_triggers', 'transaction_triggers.transaction_id', '=', 'transactions.id')
            ->leftJoin('user_daily_transactions', function ($join) {
                $join->on('user_daily_transactions.user_transaction_id', '=', 'user_transactions.id')
                    ->whereDate('user_daily_transactions.date', $this->now);
            })
            ->leftJoin('system_daily_transactions', function ($join) {
                $join->on('system_daily_transactions.transaction_id', '=', 'transactions.id')
                    ->whereDate('system_daily_transactions.date', $this->now);
            })
            ->leftJoin('system_transaction_observers', function ($join) {
                $join->on('system_transaction_observers.transaction_id', '=', 'transactions.id')
                    ->where('system_transaction_observers.is_active', true);
            })
            ->leftJoin('user_transaction_observers', 'user_transaction_observers.user_transaction_id', '=', 'user_transactions.id')
            ->where('user_transactions.user_id', $userId)
            ->where('transactions.name', $transactionType)
            ->first();
    }

    private function assertObservers(): void
    {
        if (!$this->userTransaction->system_observer_id) {
            $this->createSystemTransactionObserver();
            $this->createUserTransactionObserver();
        } else if (!$this->userTransaction->user_observer_id) {
            $this->createUserTransactionObserver();
        }
    }

    private function createSystemTransactionObserver(): void
    {
        $this->userTransaction->system_past_counter = 0;
        $this->userTransaction->start_at = $this->now;
        $this->userTransaction->end_at = $this->now->copy()->addDays($this->userTransaction->length);
        $this->userTransaction->system_observer_id = DB::table('system_transaction_observers')->insertGetId([
            'transaction_id' => $this->userTransaction->transaction_id,
            'start_at' => $this->userTransaction->start_at,
            'end_at' => $this->userTransaction->end_at,
            'past_counter' => $this->userTransaction->system_past_counter,
            'sync_at' => $this->now,
            'is_active' => true
        ]);
    }

    private function createUserTransactionObserver(): void
    {
        $this->userTransaction->user_past_counter = 0;
        $this->userTransaction->user_observer_id = DB::table('user_transaction_observers')->insertGetId([
            'user_transaction_id' => $this->userTransaction->id,
            'system_transaction_observer_id' => $this->userTransaction->system_observer_id,
            'transaction_trigger_id' => $this->userTransaction->transaction_trigger_id,
            'past_counter' => $this->userTransaction->user_past_counter,
            'sync_at' => $this->now
        ]);
    }

    private function getTriggerSubscribers(string $transactionTriggerId): array
    {
        return DB::table('trigger_subscribers')
            ->join('transaction_triggers', 'transaction_triggers.id', '=', 'trigger_subscribers.transaction_trigger_id')
            ->join('threshold_subscribers', 'threshold_subscribers.id', '=', 'trigger_subscribers.threshold_subscriber_id')
            ->where('trigger_subscribers.transaction_trigger_id', $transactionTriggerId)
            ->pluck('admin_id')->unique()->values()->all();
    }

    private function createUserDailyTransaction(string $userTransactionId, int $counter): void
    {
        DB::table('user_daily_transactions')->insert([
            'user_transaction_id' => $userTransactionId,
            'counter' => $counter,
            'date' => $this->now
        ]);
    }

    private function createSystemDailyTransaction(string $transactionId, int $counter): void
    {
        DB::table('system_daily_transactions')->insert([
            'transaction_id' => $transactionId,
            'counter' => $counter,
            'date' => $this->now
        ]);
    }

    private function validateTransactionCounter(int $counter): void
    {
        $triggerSubscribers = $this->getTriggerSubscribers($this->userTransaction->transaction_trigger_id);

        $userTotalCounter = $counter + $this->userTransaction->user_past_counter;
        $systemTotalCounter = $counter + $this->userTransaction->system_past_counter;

        $userLimitExceeded = $userTotalCounter > $this->userTransaction->user_limit + $this->userTransaction->custom_limit;
        $systemLimitExceeded = $systemTotalCounter > $this->userTransaction->system_limit;

        $this->handleThresholdTriggers($triggerSubscribers, $userLimitExceeded, $systemLimitExceeded, $counter);
    }

    private function upsertUserDailyTransactions(int $counter): void
    {
        $userTotalCounter = $counter + $this->userTransaction->user_daily_counter;
        $systemTotalCounter = $counter + $this->userTransaction->system_daily_counter;

        DB::table('user_daily_transactions')->upsert(
            [['user_transaction_id' => $this->userTransaction->id, 'counter' => $userTotalCounter, 'date' => $this->now]],
            ['user_transaction_id', 'date'],
            ['counter']
        );

        DB::table('system_daily_transactions')->upsert(
            [['transaction_id' => $this->userTransaction->transaction_id, 'counter' => $systemTotalCounter, 'date' => $this->now]],
            ['transaction_id', 'date'],
            ['counter']
        );
    }

    private function updateObservers(): void
    {
        DB::table('system_transaction_observers')
            ->where('id', $this->userTransaction->system_observer_id)
            ->incrementEach($this->systemObserverUpdatePayload, ['sync_at' => $this->now]);

        DB::table('user_transaction_observers')
            ->where('id', $this->userTransaction->user_observer_id)
            ->incrementEach($this->userObserverUpdatePayload, ['sync_at' => $this->now]);
    }

    private function handleThresholdTriggers(?array $triggerSubscribers, bool $userLimitExceeded, bool $systemLimitExceeded, int $counter): void
    {
        $routes = TransactionThresholdRoutes::ROUTES[$this->userTransaction->name];
        $notifData = [
            'transactionType' => $this->userTransaction->name,
            'userEmail' => auth()->user()->email,
            'isAllowed' => $this->userTransaction->allow_action,
            'route' => $routes['notif'],
        ];

        foreach (['User' => $userLimitExceeded, 'System' => $systemLimitExceeded] as $type => $exceeded) {
            if (!$exceeded) {
                $this->setPastCounters($counter);
                continue;
            }

            $this->addHits($type);

            if ($this->userTransaction->notify_subscriber && !empty($triggerSubscribers))
                TransactionThresholdNotificationService::instance()->{"sendAdmin{$type}LimitExceededNotif"}($triggerSubscribers, $notifData);

            if (!$this->userTransaction->allow_action) {
                $this->reject($counter);
            } else {
                $this->approve($counter);
                $this->setPastCounters($counter);
            }
        }
    }

    private function cancelIntent(?string $intent): void
    {
        if ($intent) {
            PaymentIntentProvider::instance()->cancelIntent($intent);
        }
    }

    private function addHits(string $type): void
    {
        if ($type === 'User') $this->userObserverUpdatePayload['hits'] = 1;
        else if ($type === 'System') $this->systemObserverUpdatePayload['hits'] = 1;
    }

    private function handleTransactionReject(): void
    {
        $routes = TransactionThresholdRoutes::ROUTES[$this->userTransaction->name];

        if ($this->sendErrorPage) {
            $this->cancelIntent($this->intent);
            throw new TransactionThresholdException(403, "We couldn't complete your transaction. Please try again later or contact support for assistance.", 'Transaction unavailable.', route($routes['exception']), $routes['label']);
        }
    }

    private function setPastCounters(int $counter): void
    {
        $this->userObserverUpdatePayload['past_counter'] = $counter;
        $this->systemObserverUpdatePayload['past_counter'] = $counter;
    }

    private function reject(int $counter): void
    {
        $this->sendErrorPage = true;
        $this->userObserverUpdatePayload['rejected'] = $counter;
        $this->systemObserverUpdatePayload['rejected'] = $counter;
    }

    private function approve(int $counter): void
    {
        $this->userObserverUpdatePayload['approved'] = $counter;
        $this->systemObserverUpdatePayload['approved'] = $counter;
        $this->setPastCounters($counter);
    }
}
