<?php

namespace App\Modules\AccountCredit\Services;

use App\Modules\BankTransfer\Services\BankTransferService;
use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ViewAccountCreditService
{
    use CursorPaginate;

    private $pageLimit = 20;

    public static function instance(): self
    {
        $viewAccountCreditService = new self;

        return $viewAccountCreditService;
    }

    public function getIndexData()
    {
        $fundItems        = BankTransferService::instance()->getPendingBankTransfersAddCredit();
        $balance          = $this->getBalance();
        $transactionItems = $this->getTransactionItems();

        return [
            'fundItems'        => $fundItems,
            'balance'          => $balance,
            'transactionItems' => $transactionItems,
        ];
    }

    public function getBalance()
    {
        $latestBlock = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        return $latestBlock->running_balance ?? 0;
    }

    public function getViewQuery()
    {
        return DB::table('account_credits')
            ->join('payment_services', 'payment_services.account_credit_id', '=', 'account_credits.id')
            ->leftJoin('payment_invoices', 'payment_invoices.payment_service_id', '=', 'payment_services.id')
            ->leftJoin('market_place_payment_invoices', 'market_place_payment_invoices.payment_service_id', '=', 'payment_services.id')
            ->leftJoin('payment_summaries', 'payment_summaries.payment_service_id', '=', 'payment_services.id')
            ->leftJoin('payment_reimbursements', 'payment_reimbursements.payment_service_id', '=', 'payment_services.id')
            ->leftJoin('market_place_reimbursements', 'market_place_reimbursements.payment_service_id', '=', 'payment_services.id')
            ->where('account_credits.user_id', $this->getUserId())
            ->select(
                'account_credits.*',
                'payment_services.id as payment_service_id',
                'payment_services.stripe_id',
                'payment_services.account_credit_id',
                'payment_services.bank_transfer_id',
                'payment_services.system_credit_id',
                'payment_invoices.id as payment_invoice_id',
                'payment_invoices.total_amount as invoice_total_amount',
                'payment_invoices.paid_amount as invoice_paid_amount',
                'market_place_payment_invoices.id as payment_market_place_invoice_id',
                'market_place_payment_invoices.total_amount as market_place_invoice_total_amount',
                'market_place_payment_invoices.paid_amount as market_place_invoice_paid_amount',
                'payment_reimbursements.id as payment_reimbursement_id',
                'payment_reimbursements.total_amount as reimbursement_total_amount',
                'market_place_reimbursements.id as payment_market_place_reimbursement_id',
                'market_place_reimbursements.total_amount as market_place_reimbursement_total_amount',
                'payment_summaries.id as payment_summary_id',
                'payment_summaries.total_amount as summary_total_amount',
                'payment_summaries.paid_amount as summary_paid_amount',
                'payment_summaries.type as summary_type',
                'payment_summaries.name as summary_name',
            );
    }

    public function getTransactionItems()
    {
        $builder = $this->getViewQuery()
            ->orderBy('block_index', 'desc')
            ->paginate($this->pageLimit);

        return CursorPaginate::cursor($builder);
    }

    public function getTransactionItemsOld()
    {
        $builder = DB::table('account_credits')
            ->where('user_id', $this->getUserId())
            ->orderBy('block_index', 'desc')
            ->paginate($this->pageLimit);

        return CursorPaginate::cursor($builder);
    }

    // PRIVATE FUNCTION

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }
}
