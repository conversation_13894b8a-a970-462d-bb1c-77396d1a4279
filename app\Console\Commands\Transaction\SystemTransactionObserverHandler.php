<?php

namespace App\Console\Commands\Transaction;

use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Exception;

class SystemTransactionObserverHandler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transactions:system-transactions-observer-handler';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the update of the system_transaction_observers table.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'SystemTransactionObserverHandler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('SystemTransactionObserverHandler: Checking the system transaction observer...');

        $now = Carbon::now();
        $expiredSystemObservers = $this->expiredObserversQuery($now);

        if (!$expiredSystemObservers->exists()) {
            app(AuthLogger::class)->info('SystemTransactionObserverHandler: Done. No expired system transaction observers found.');
            return;
        }

        $transactions = $expiredSystemObservers->select('transactions.id', 'transactions.length')->get();
        $expiredSystemObservers->update(['is_active' => false]);
        $this->createNewObserver($transactions, $now);

        app(AuthLogger::class)->info('SystemTransactionObserverHandler: Done');
    }

    private function expiredObserversQuery(Carbon $now): Builder
    {
        return DB::table('system_transaction_observers')
            ->join('transactions', 'transactions.id', '=', 'system_transaction_observers.transaction_id')
            ->where('system_transaction_observers.is_active', true)
            ->whereDate('system_transaction_observers.end_at', '<', $now);
    }

    private function createNewObserver(Collection $transactions, Carbon $now)
    {
        $payload =  $transactions->map(fn($transaction) => [
            'transaction_id' => $transaction->id,
            'start_at' => $now,
            'end_at' => $now->copy()->addDays($transaction->length),
            'past_counter' => 0,
            'sync_at' => $now,
            'is_active' => true
        ])->toArray();

        DB::table('system_transaction_observers')->insert($payload);
    }
}
