<?php

namespace App\Modules\Domain\Requests\Nameserver;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Rules\NameserverChecker;
use Illuminate\Foundation\Http\FormRequest;

class NameserverCheckRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name.*' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'distinct',
                "regex:/^(?=.{1,253}$)([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/",
                new NameserverChecker,
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'name.*' => 'nameserver',
        ];
    }

    public function messages(): array
    {
        return [];
    }

    public function check()
    {
        // $response = NameserverService::instance()->check($this->name);
        // if (! $response['success']) {
        //     $response['message'];
        // }

        // return $response['success'];
        app(AuthLogger::class)->info('checkNameserver: Running...');
    }
}
