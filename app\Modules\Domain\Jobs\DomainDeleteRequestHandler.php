<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Services\UpdateServices\DeleteRequestHandlerService;
use App\Modules\PendingDelete\Services\DomainEppDeleteJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Exception;
use stdClass;
use Throwable;

class DomainDeleteRequestHandler implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    const ACTION = ['REJECT' => 'reject', 'CANCEL' => 'cancel'];
    private $domain;
    private $action;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(stdClass $domain, string $action)
    {
        $this->domain = $domain;
        $this->action = $action;

        $this->onConnection(QueueConnection::DOMAIN_DELETE_REQUEST_HANDLER);
        $this->onQueue(QueueTypes::DOMAIN_DELETE_REQUEST_HANDLER[$action]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return (int) $this->domain->domain_cancellation_request_id;
    }

    public function handle(): void
    {
        try {
            if ($this->action === self::ACTION['REJECT']) {
                DeleteRequestHandlerService::instance()->processRejectRequest($this->domain, $this->action);
            } else if ($this->action === self::ACTION['CANCEL']) {
                DeleteRequestHandlerService::instance()->processCancelRequest($this->domain, $this->action);
            }
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());
            app(AuthLogger::class)->info('number of attempts: ' . $this->attempts());
            throw $e;
        }
    }

    public function backoff(): array
    {
        return [30, 60];
    }

    public function failed(?Throwable $exception): void
    {
        app(AuthLogger::class)->error($exception->getMessage());
        // $this->dispatchFailedDomainHistory();
    }

    // private function dispatchFailedDomainHistory(): void
    // {
    //     $message = 'Domain "' . $this->params['domainName'] . '" deletion failed during pending deletion job';

    //     DB::client()->table('domain_transaction_histories')->insert([
    //         'domain_id' => $this->params['domainId'],
    //         'type'      => 'DOMAIN_DELETED',
    //         'user_id'   => $this->params['userId'],
    //         'status'    => 'failed',
    //         'message'   => $message,
    //         'payload'   => json_encode($this->params),
    //         'created_at' => now(),
    //         'updated_at' => now(),
    //     ]);
    // }
}
