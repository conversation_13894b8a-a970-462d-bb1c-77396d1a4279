<?php

namespace App\Console\Commands\Domain;

use Illuminate\Console\Command;
use App\Modules\Domain\Services\DomainDeletionService;
use App\Modules\CustomLogger\Services\AuthLogger;

class ProcessDomainDeletionRefunds extends Command
{
    protected $signature = 'domain:deletion-refund';

    protected $description = 'Process refunds for all approved domain deletions';

    public function handle()
    {

        $registrationRefunds = DomainDeletionService::instance()->getRefundableRegistrationDomains();
        $renewalRefunds = DomainDeletionService::instance()->getRefundableRenewalDomains();

        if (empty($registrationRefunds) && empty($renewalRefunds)) {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: No eligible refunds to process.');
            return 0;
        }

        if (!empty($registrationRefunds)) {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Processing ' . count($registrationRefunds) . ' registration refunds...');
            DomainDeletionService::instance()->refundRegistration($registrationRefunds);
        }


        if (!empty($renewalRefunds)) {
            app(AuthLogger::class)->info('ProcessDomainDeletionRefunds: Processing ' . count($renewalRefunds) . ' renewal refunds...');
            DomainDeletionService::instance()->refundRenewal($renewalRefunds);
        }

        $this->info('Domain deletion refunds processed successfully.');
        return 0;
    }
}
