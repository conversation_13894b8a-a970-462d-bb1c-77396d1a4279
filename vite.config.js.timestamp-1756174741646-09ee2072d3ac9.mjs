// vite.config.js
import { defineConfig } from "file:///mnt/hdd/xampp/htdocs/sd-client/node_modules/vite/dist/node/index.js";
import laravel from "file:///mnt/hdd/xampp/htdocs/sd-client/node_modules/laravel-vite-plugin/dist/index.mjs";
import react from "file:///mnt/hdd/xampp/htdocs/sd-client/node_modules/@vitejs/plugin-react/dist/index.mjs";
var isProduction = process.env.NODE_ENV === "production";
var vite_config_default = defineConfig({
  server: {
    host: "www.strangedomains.local",
    port: 5173,
    strictPort: true,
    cors: true,
    hmr: {
      host: "www.strangedomains.local"
    }
  },
  plugins: [
    laravel({
      input: "resources/js/app.jsx",
      refresh: true,
      detectTls: "strangedomains.com"
    }),
    react()
  ],
  build: {
    sourcemap: !isProduction
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCIvbW50L2hkZC94YW1wcC9odGRvY3Mvc2QtY2xpZW50XCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCIvbW50L2hkZC94YW1wcC9odGRvY3Mvc2QtY2xpZW50L3ZpdGUuY29uZmlnLmpzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9tbnQvaGRkL3hhbXBwL2h0ZG9jcy9zZC1jbGllbnQvdml0ZS5jb25maWcuanNcIjtpbXBvcnQgeyBkZWZpbmVDb25maWcgfSBmcm9tIFwidml0ZVwiO1xuaW1wb3J0IGxhcmF2ZWwgZnJvbSBcImxhcmF2ZWwtdml0ZS1wbHVnaW5cIjtcbmltcG9ydCByZWFjdCBmcm9tIFwiQHZpdGVqcy9wbHVnaW4tcmVhY3RcIjtcblxuY29uc3QgaXNQcm9kdWN0aW9uID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJztcblxuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKHtcbiAgICBzZXJ2ZXI6IHtcbiAgICAgICAgaG9zdDogJ3d3dy5zdHJhbmdlZG9tYWlucy5sb2NhbCcsXG4gICAgICAgIHBvcnQ6IDUxNzMsXG4gICAgICAgIHN0cmljdFBvcnQ6IHRydWUsXG4gICAgICAgIGNvcnM6IHRydWUsXG4gICAgICAgIGhtcjoge1xuICAgICAgICAgICAgaG9zdDogJ3d3dy5zdHJhbmdlZG9tYWlucy5sb2NhbCcsXG4gICAgICAgIH0sXG4gICAgfSxcbiAgICBwbHVnaW5zOiBbXG4gICAgICAgIGxhcmF2ZWwoe1xuICAgICAgICAgICAgaW5wdXQ6IFwicmVzb3VyY2VzL2pzL2FwcC5qc3hcIixcbiAgICAgICAgICAgIHJlZnJlc2g6IHRydWUsXG4gICAgICAgICAgICBkZXRlY3RUbHM6ICdzdHJhbmdlZG9tYWlucy5jb20nXG4gICAgICAgIH0pLFxuICAgICAgICByZWFjdCgpLFxuICAgIF0sXG4gICAgYnVpbGQ6IHtcbiAgICAgICAgc291cmNlbWFwOiAhaXNQcm9kdWN0aW9uLFxuICAgIH1cbn0pO1xuIl0sCiAgIm1hcHBpbmdzIjogIjtBQUErUSxTQUFTLG9CQUFvQjtBQUM1UyxPQUFPLGFBQWE7QUFDcEIsT0FBTyxXQUFXO0FBRWxCLElBQU0sZUFBZSxRQUFRLElBQUksYUFBYTtBQUU5QyxJQUFPLHNCQUFRLGFBQWE7QUFBQSxFQUN4QixRQUFRO0FBQUEsSUFDSixNQUFNO0FBQUEsSUFDTixNQUFNO0FBQUEsSUFDTixZQUFZO0FBQUEsSUFDWixNQUFNO0FBQUEsSUFDTixLQUFLO0FBQUEsTUFDRCxNQUFNO0FBQUEsSUFDVjtBQUFBLEVBQ0o7QUFBQSxFQUNBLFNBQVM7QUFBQSxJQUNMLFFBQVE7QUFBQSxNQUNKLE9BQU87QUFBQSxNQUNQLFNBQVM7QUFBQSxNQUNULFdBQVc7QUFBQSxJQUNmLENBQUM7QUFBQSxJQUNELE1BQU07QUFBQSxFQUNWO0FBQUEsRUFDQSxPQUFPO0FBQUEsSUFDSCxXQUFXLENBQUM7QUFBQSxFQUNoQjtBQUNKLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
