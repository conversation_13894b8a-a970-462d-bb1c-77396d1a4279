<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('user_transaction_observers', 'hits')) {
            Schema::table('user_transaction_observers', function (Blueprint $table) {
                $table->integer('hits')->default(0);
            });
        }

        if (!Schema::hasColumn('user_transaction_observers', 'approved')) {
            Schema::table('user_transaction_observers', function (Blueprint $table) {
                $table->integer('approved')->default(0);
            });
        }

        if (!Schema::hasColumn('user_transaction_observers', 'rejected')) {
            Schema::table('user_transaction_observers', function (Blueprint $table) {
                $table->integer('rejected')->default(0);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_transaction_observers', function (Blueprint $table) {
            $table->dropColumn(['hits', 'approved', 'rejected']);
        });
    }
};
