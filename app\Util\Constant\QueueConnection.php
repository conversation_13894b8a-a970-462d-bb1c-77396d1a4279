<?php

namespace App\Util\Constant;

final class QueueConnection
{
    public final const DEFAULT = 'default';

    public final const DOMAIN_REGISTRATION = 'domain_registration_jobs';

    public final const DOMAIN_RENEWAL = 'domain_renewal_jobs';

    public final const DOMAIN_UPDATE = 'domain_update_jobs';

    public final const DOMAIN_CONTACTS_UPDATE = 'domain_contacts_update_jobs';

    public final const DOMAIN_TRANSFER = 'domain_transfer_jobs';

    public final const DOMAIN_REDEMPTION = 'domain_redemption_jobs';

    public final const DOMAIN_AUTHCODE_REQUEST = 'domain_authcode_request_jobs';

    public final const DOMAIN_AUTHCODE_UPDATE = 'domain_authcode_update_jobs';

    public final const DOMAIN_TRANSFER_RESPONSE = 'send_transfer_request_response_jobs';

    public final const DOMAIN_TRANSFER_CANCEL = 'cancel_domain_transfer_jobs';

    public final const DOMAIN_TRANSFER_POLL_UPDATE = 'poll_update_domain_transfer_jobs';

    public final const CONTACT_REGISTRATION = 'contact_registration_jobs';

    public final const CONTACT_UPDATE = 'contact_update_jobs';

    public final const DOMAIN_SCHEDULE_EXPIRY = 'domain_exp_notif_sched_jobs';

    public final const MAIL_JOB = 'mail_jobs';

    public final const PAYMENT_JOB = 'payment_jobs';

    public final const UPDATE_ON_LOGIN = 'update_on_login_jobs';

    public final const DOMAIN_REFRESH = 'domain_refresh_jobs';

    public final const OTP_MAIL_JOBS = 'otp_mail_jobs';

    public final const USER_DOMAIN_EXPORT_JOBS = 'user_domain_export_jobs';

    public final const AUTHENTICATION_ATTEMPTS_JOBS = 'authentication_attempts_jobs';

    public final const STRIPE_IDENTITY = 'stripe_identity_jobs';

    public final const MARKET_PLACE_JOBS = 'market_place_jobs';

    public final const DOMAIN_CLASSIFICATION_JOBS = 'domain_classification_api_jobs';

    public final const DOMAIN_DELETE_REQUEST_HANDLER = 'domain_delete_request_handler_jobs';
}
