//* PACKAGES 
import React, { useState, useRef } from 'react';
import { router } from "@inertiajs/react";

//* ICONS
import { BsLock, BsUnlock } from "react-icons/bs";
import { MdRefresh } from "react-icons/md";
import { TbWorldSearch, TbWorld, TbFileTypeCsv, TbFileStack, TbFile } from "react-icons/tb";

//* COMPONENTS
import DropDownContainer from "@/Components/DropDownContainer";

//* STATE
//...

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import SecondaryButton from '@/Components/SecondaryButton';
import isDomainExpiredByDays from '@/Util/isDomainExpiredByDays';

//* CONSTANTS
import _DomainStatus from "@/Constant/_DomainStatus";
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

//* PARTIALS
//...

export default function PartialButtonActionsGroup(
    {
        //! VARIABLES
        user,
        itemCount,

        //! STATES 
        selectedItems,
        selectedIds,

        //! EVENTS
        handleRequestAuthcodeBtn,
        handleModalDomainFormExportShow,
    }
) {
    //! REFS 
    const refItemsDropdown = useRef();

    //! STATES
    const [showDropdownExport, setShowDropdownExport] = useState(false);

    //! USE EFFECTS
    useOutsideClick(
        refItemsDropdown,
        () => {
            setShowDropdownExport(false);
        }
    );

    const iconSizeClass = 'h-5 w-5';

    const itemsAction =
        [
            {
                label: 'lock',
                icon: <BsLock className={`${iconSizeClass}`} />,
                isProcessing: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                isDisabled: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                onClick: () => handleLockBtn(true)
            },
            {
                icon: <BsUnlock className={`${iconSizeClass}`} />,
                label: 'unlock',
                isProcessing: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                isDisabled: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                onClick: () => handleLockBtn(false)
            },
            {
                label: 'renew',
                icon: <MdRefresh className={`${iconSizeClass}`} />,
                isProcessing: selectedIds.length == 0 || selectedItems.some(item => item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                isDisabled: selectedIds.length == 0 || selectedItems.some(item => item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                onClick: () => handleRenewBtn()
            },
            {
                label: 'restore',
                icon: <MdRefresh className={`${iconSizeClass}`} />,
                isProcessing: selectedIds.length == 0 || selectedItems.some(item => item.status != 'REDEMPTION'),
                isDisabled: selectedIds.length == 0 || selectedItems.some(item => item.status != 'REDEMPTION'),
                onClick: () => handleRestoreBtn()
            },
            {
                label: 'request auth code',
                icon: <TbWorld className={`${iconSizeClass}`} />,
                isProcessing: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD)) || isDomainExpiredByDays(item.expiry, 44)),
                isDisabled: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD)) || isDomainExpiredByDays(item.expiry, 44)),
                onClick: () => handleRequestAuthcodeBtn(selectedItems)
            },
            {
                label: 'update nameservers',
                icon: <TbWorldSearch className={`${iconSizeClass}`} />,
                isProcessing: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                isDisabled: selectedIds.length == 0 || selectedItems.some(item => item.status == 'EXPIRED' || item.status == 'REDEMPTION' || (item.client_status && item.client_status.includes(_DomainStatus.STATUS.HOLD))),
                onClick: () => handleUpdateNameserver()
            }
        ];

    const itemsDropdown =
        [
            {
                ref: refItemsDropdown,
                label: 'export',
                isProcessing: itemCount == 0,
                isDisabled: itemCount == 0,
                icon: <TbFileTypeCsv className={`${iconSizeClass}`} />,
                onClick: () => {
                    if (showDropdownExport == false) {
                        setShowDropdownExport(true);
                    }
                    else {
                        setShowDropdownExport(false);
                    }
                },
                dropdownItems:
                    [
                        {
                            label: 'all',
                            isProcessing: itemCount == 0,
                            isDisabled: itemCount == 0,
                            icon: <TbFileStack className={`${iconSizeClass}`} />,
                            onClick: () => {
                                handleModalDomainFormExportShow('all');
                                setShowDropdownExport(false);
                            }
                        },
                        {
                            label: 'selected',
                            isProcessing: selectedIds.length == 0,
                            isDisabled: selectedIds.length == 0,
                            icon: <TbFile className={`${iconSizeClass}`} />,
                            onClick: () => {
                                handleModalDomainFormExportShow('selectedItems')
                                setShowDropdownExport(false);
                            }
                        }
                    ]
            }
        ];

    //! FUNCTIONS
    const handleRenewBtn = () => {
        router.post(route("domain.renew.confirm"), {
            user_id: user.id,
            domains: selectedItems,
        }, {
            replace: true,
            preserveState: false,
        });
    };

    const handleRestoreBtn = () => {
        router.post(route("domain.redeem.confirm"), {
            user_id: user.id,
            domains: selectedItems,
        }, {
            replace: true,
            preserveState: false,
        });
    };

    const handleLockBtn = (disableLock) => {
        router.post(route("domain.lock.confirm"), {
            user_id: user.id,
            domains: selectedItems,
            domains_list: selectedIds,
            isDisable: disableLock,
        });
    };

    const handleUpdateNameserver = () => {
        router.post(route("domain.select-nameserver"), {
            user_id: user.id,
            domains: selectedItems,
            domains_list: selectedIds,
        });
    };

    return (
        <div
            className="flex items-center gap-x-5 justify-center py-2"
        >
            {
                itemsAction.map(
                    (item, index) => {
                        return (
                            <SecondaryButton
                                key={index}
                                processing={item.isProcessing}
                                onClick={item.onClick}
                            >
                                {item.label}
                            </SecondaryButton>
                        );
                    }
                )
            }

            {
                itemsDropdown.map(
                    (item, index) => {
                        return (
                            <div
                                key={index}
                                ref={refItemsDropdown}
                                className="flex items-center justify-between relative"
                            >
                                <SecondaryButton
                                    processing={item.isProcessing}
                                    onClick={item.onClick}
                                >
                                    {item.label}
                                </SecondaryButton>
                                <DropDownContainer
                                    show={showDropdownExport}
                                    className=" bottom-[3rem] bg-white h-28 top-12 px-2"
                                >
                                    {
                                        item.dropdownItems.map(
                                            (dropdownItem, dropdownItemIndex) => {
                                                return (
                                                    <div
                                                        key={dropdownItemIndex}
                                                        className="inline-flex w-full items-center justify-between relative"
                                                    >
                                                        <button
                                                            className={`
                                                                flex gap-2 align-middle  items-center px-5 py-3 w-full 
                                                                rounded
                                                                ease-in duration-200
                                                                font-bold
                                                                ${dropdownItem.isDisabled == true || dropdownItem.isProcessing == true ? 'text-gray-200 cursor-default' : 'text-primary hover:bg-blue-50'}
                                                            `}
                                                            processing={dropdownItem.isProcessing.toString()}
                                                            disabled={dropdownItem.isDisabled}
                                                            onClick={dropdownItem.onClick}
                                                        >
                                                            {dropdownItem.icon}
                                                            <span
                                                                className='capitalize'
                                                            >
                                                                {dropdownItem.label}
                                                            </span>
                                                        </button>
                                                    </div>
                                                );
                                            }
                                        )
                                    }
                                </DropDownContainer>
                            </div>
                        );
                    }
                )
            }
        </div>
    );
}
