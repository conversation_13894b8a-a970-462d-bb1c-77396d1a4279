<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('users')
            ->whereNull('primary_payment_method')
            ->update(['primary_payment_method' => 'account_credit']);

        Schema::table('users', function (Blueprint $table) {
            $table->string('primary_payment_method')->default('account_credit')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('primary_payment_method')->default(null)->change();
        });
    }
};
