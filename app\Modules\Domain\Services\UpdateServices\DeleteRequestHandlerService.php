<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\DomainHistoryEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Jobs\DomainDeleteRequestHandler;
use App\Modules\Domain\Services\EppDomainService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use stdClass;

class DeleteRequestHandlerService
{
    use UserLoggerTrait;

    const ACTION = ['REJECT' => 'reject', 'CANCEL' => 'cancel'];

    private $userId;
    private $userEmail;
    private $dispatchTenSeconds = 10;
    private $maxDaysBeforeDeletion = 45;
    private $gracePeriodDays = 40;
    private $maxRequestDuration = 5;

    public static function instance(): self
    {
        $deleteRequestHandlerService = new self;

        return $deleteRequestHandlerService;
    }

    public function jobDispatch(Collection $domains, string $action)
    {
        if ($action === self::ACTION['CANCEL']) {
            PostAutoRenewalGracePeriodService::instance()->setForDeletion($domains);
        }

        foreach ($domains as $domain) {
            DomainDeleteRequestHandler::dispatch($domain, $action);
        }
    }

    public function processCancelRequest(stdClass $domain): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete cancelled by " . $adminContext['email'];

        $this->cancelDomainDeletionRequest($domain, $adminContext, $supportNote);
        $this->logDomainHistory($domain, 'DOMAIN_UPDATED', 'success', 'Domain deletion request cancelled by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');
        // $this->sendCancellationNotification($domain, $adminContext);
    }

    public function processRejectRequest(stdClass $domain): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete rejected by " . $adminContext['email'];

        $this->rejectDomainDeletionRequest($domain, $adminContext, $supportNote);
        $this->updateDomainProperties($domain);
        $this->logDomainHistory($domain, 'DOMAIN_UPDATED', 'success', 'Domain deletion request rejected by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');
        // $this->sendRejectionNotification($data, $adminContext);
    }

    public function getExpiredDeleteRequests()
    {
        $now = Carbon::now();

        $results = $this->baseQuery()->addSelect(
            DB::raw("CASE
                WHEN domains.server_renew_at < '{$now->copy()->subDays($this->maxDaysBeforeDeletion)}'
                    AND domains.client_renew_at < '{$now->copy()->subDays($this->gracePeriodDays)}'
                    AND domains.expiry < '{$now->copy()->subDays($this->gracePeriodDays)->getTimestampMs()}'
                THEN 'delete'
                WHEN domain_cancellation_requests.requested_at <= '{$now->copy()->subDays($this->maxRequestDuration)}'
                THEN 'reject'
                ELSE 'none'
            END as action_type")
        )->get();

        return [
            'toDelete' => $results->where('action_type', 'delete')->values(),
            'toReject' => $results->where('action_type', 'reject')->values(),
        ];
    }

    // PRIVATE FUNCTIONS

    private function cancelDeleteRequest(Collection $domainsList)
    {
        $domainUpdateIds = $domainsList->pluck('id')->toArray();

        DB::table('domain_cancellation_requests')
            ->whereIn('domain_id', $domainUpdateIds)
            ->update(['deleted_at' => Carbon::now()]);
    }

    private function rejectDeleteRequest(Collection $domainsList)
    {
        $domainUpdateIds = $domainsList->pluck('id')->toArray();

        DB::table('domain_cancellation_requests')
            ->whereIn('domain_id', $domainUpdateIds)
            ->update(['deleted_at' => Carbon::now()]);
    }

    private function baseQuery(): Builder
    {
        return DB::table('domain_cancellation_requests')
            ->select(
                'domains.*',
                'registered_domains.id as registered_domain_id',
                'users.email as user_email',
                'users.id as user_id',
                'users.first_name',
                'users.last_name',
                'domain_cancellation_requests.id as domain_cancellation_request_id',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at'
            )
            ->join('registered_domains', 'registered_domains.id', '=', 'domain_cancellation_requests.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('registered_domains.status', UserDomainStatus::OWNED)
            ->where('domains.status', DomainStatus::IN_PROCESS)
            ->whereNull('domain_cancellation_requests.deleted_at');
    }

    private function getAdminContext(): array
    {
        return [
            'id' => 1,
            'name' => 'System',
            'email' => '<EMAIL>'
        ];
    }

    private function logDomainHistory(stdClass $domain, string $type, string $status, string $message): void
    {
        event(new DomainHistoryEvent([
            'domain_id' => $domain->id,
            'type' => $type,
            'status' => $status,
            'user_id' => $domain['userID'] ?? null,
            'message' => $message,
            'payload' => json_encode($domain),
        ]));
    }

    private function cancelDomainDeletionRequest(stdClass $domain, array $adminContext, string $supportNote): void
    {
        DB::table('domain_cancellation_requests')
            ->where('registered_domain_id', $domain->registered_domain_id)
            ->update([
                'support_agent_id' => $adminContext['id'],
                'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
                'support_note' => $supportNote,
                'feedback_date' => null,
                'deleted_at' => Carbon::now(),
            ]);
    }

    private function rejectDomainDeletionRequest(stdClass $domain, array $adminContext, string $supportNote): void
    {
        DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $domain->registered_domain_id)
            ->update([
                'support_agent_id' => $adminContext['id'],
                'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
                'support_note' => $supportNote,
                'feedback_date' => Carbon::now(),
                'deleted_at' => null,
            ]);
    }

    private function updateDomainProperties(stdClass $domain): void
    {
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain->name);

        if (!isset($datastoreInfoResponse['data']) || is_null($datastoreInfoResponse['data'])) {
            app(AuthLogger::class)->error("Domain {$domain->name} not found in datastore. Response: " . json_encode($datastoreInfoResponse));
            throw new Exception("Domain {$domain->name} not found in datastore");
        }

        $datastoreInfo = $datastoreInfoResponse['data'];

        $expiryDate = Carbon::parse($datastoreInfo['expiry']);
        $isExpired = $expiryDate->isPast();
        $status = $isExpired ? DomainStatus::EXPIRED : DomainStatus::ACTIVE;

        $this->updateDomainStatus($domain, $status);
    }

    private function updateDomainStatus(stdClass $domain, string $status): void
    {
        $timestamp = Carbon::now();

        DB::client()->table('domains')
            ->where('id', $domain->id)
            ->update([
                'status' => $status,
                'updated_at' => $timestamp,
            ]);
    }

    // private function sendCancellationNotification(array $data, array $adminContext): void
    // {
    //     $message = 'Your domain deletion request for "' . $data['domainName'] . '" has been cancelled by ' . $adminContext['name'] . '.';

    //     $this->sendUserNotification($data, 'Domain Deletion Request Cancelled', $message);

    //     $emailBody = 'Your domain deletion request for "' . $data['domainName'] . '" has been cancelled by our support team. Your domain remains active.';
    //     $this->sendUserEmail($data, 'Domain Deletion Request Cancelled', $emailBody);
    // }
}
