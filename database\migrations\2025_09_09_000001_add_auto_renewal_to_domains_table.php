<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->boolean('auto_renewal_enabled')->default(true)->after('year_length');
            $table->timestamp('auto_renewal_attempt_at')->nullable()->after('auto_renewal_enabled');
            $table->integer('auto_renewal_attempts')->default(0)->after('auto_renewal_attempt_at');
            $table->text('auto_renewal_failure_reason')->nullable()->after('auto_renewal_attempts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropColumn([
                'auto_renewal_enabled',
                'auto_renewal_attempt_at',
                'auto_renewal_attempts',
                'auto_renewal_failure_reason'
            ]);
        });
    }
};
