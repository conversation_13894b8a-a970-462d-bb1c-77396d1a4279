<?php

namespace App\Modules\Cart\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Cart\Constants\DomainThreshold;
use App\Modules\Cart\Requests\MultiCheckoutRequest;
use App\Modules\Cart\Services\MultiCheckout\MultiCartService;
use App\Modules\PaymentService\Constants\PaymentServiceType;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\CustomLogger\Services\AuthLogger;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class MultiCheckoutController extends Controller
{
    public function getCheckoutSummary()
    {
        $threshold = MultiCartService::instance()->getDomainThreshold();
        $DomainThresholdCount = MultiCartService::instance()->getRegistrationThresholdData();

        $data = ($DomainThresholdCount >= $threshold->threshold_limit)
            ? MultiCartService::instance()->displayThresholdNotif()
            : MultiCartService::instance()->getMultiCartSummary();

        return Inertia::render('Domain/MultiCheckout', $data);
    }

    public function store(MultiCheckoutRequest $request)
    {
        try {
        $summaryId = $request->store();

        //! IF BANK TRANSFER
        if ($request->payment_service_type == PaymentServiceType::BANK_TRANSFER)
        {
            return Inertia::render('Notice/ConfirmationMessage', [
                'message' => 'Domain(s) has been reserved. Please transfer the exact amount indicated for admin approval of your Bank Transfer for acquisition.',
                'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
            ]);
        }

        return Inertia::render('Notice/ConfirmationMessage', [
            'message' => 'Payment successful. Domain acquisition is in process.',
            'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
        ]);
        } catch (\Exception $e) {
            // Handle exception
            throw $e;
        }
    }
}
