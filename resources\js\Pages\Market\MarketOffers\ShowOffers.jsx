import React, { useEffect, useState } from 'react'
import UserLayout from '@/Layouts/UserLayout'
import DataTable from 'react-data-table-component';
import OfferHistoryPopup from '../components/OfferHistoryPopup';
import { FaSearch } from "react-icons/fa";
import { PiCurrencyDollarDuotone } from "react-icons/pi";
import { FaBalanceScaleLeft } from "react-icons/fa";
import { ImCross } from "react-icons/im";
import ConfirmPopUp from '../components/ConfirmPopUp';
import CounterOfferPopup from '../components/CounterOfferPopup';

export default function ShowOffers({myoffers}) {

    const [offers, setOffers] = useState([]);
    const [modal, showModal] = useState(false);
    const [status, setStatus] = useState(false);
    const [cmodal, showCModal] = useState(false);
    const [comodal, showCOModal] = useState(false);

    const [domain, setDomain] = useState({name: '', created_at: new Date(), offer_price: 0});

    const statusColors = {
        waiting: "bg-gray-500 text-white",
        offer_closed: "bg-red-500 text-white",
        counter_offer: "bg-yellow-500 text-black",
        offer_accepted: "bg-green-500 text-white",
        offer_rejected: "bg-pink-500 text-white",
        user_counter_offer: "bg-orange-500 text-white",

        paid_hold_pending: "bg-blue-500 text-white",
        paid_order_pending: "bg-purple-500 text-white",
        paid_transfer_pending: "bg-teal-500 text-white",
        paid_transfer_requested: "bg-indigo-500 text-white",
        paid_transfer_completed: "bg-lime-500 text-black",
    };

    const columns = [
        {
            id: 'domain',
            name: 'Domain',
            left: "true",
            selector: row => row.domain_name,
            sortable: true,
        },
        {
            id: 'price',
            name: 'Initial Offer',
            left: "true",
            selector: row => row.offer_price,
            cell: row => `$${row.offer_price}`,
            sortable: true,
             width: '126px'
        },
        {
            id: 'status_change',
            name: 'Last Status Update',
            left: "true",
            selector: row => row.updated_at,
            cell: row => `${new Date(row.updated_at).toLocaleString()}`,
            sortable: true,
            width: '180px'
        },
        {
            id: 'status',
            name: 'Status',
            left: "true",
            selector: row => row.offer_status,
            cell: row => getStatus(row.offer_status),
            sortable: true,
        },
        {
            id: 'current',
            name: 'Buy Now Price',
            left: "true",
            selector: row => row.counter_offer_price,
            cell: row => `${row.counter_offer_price > 0 ? `$${row.counter_offer_price}` : 'NA'}`,
            sortable: true,
            width: '150px'
        },
        {
            id: 'actions',
            name: 'Actions',
            selector: row => getDetailButton(row),
            sortable: true,
        },
    ];

    const handlePopUp = (row, status) => {
        setDomain(row)

        if(status == 'pay' || status == 'close') {
            showCModal(true);
            setStatus(status)
        } else if(status == 'counter_offer') {
            showCOModal(true);
        } else showModal(true)
    }

    const getDetailButton = (row) => {
        return <div className='flex gap-1 font-bold'>
            <div className='has-tooltip'>
                <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-primary px-1 -mt-8'>View History</span>
                <button onClick={() => { handlePopUp(row) }} className='bg-primary rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                    <FaSearch className=' font-bold' />
                </button>
            </div>
            {
                (row.offer_status == 'counter_offer') ? <div className='has-tooltip'>
                    <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-yellow-700 px-1 -mt-8'>Counter Offer</span>
                    <button onClick={() => { handlePopUp(row, 'counter_offer') }} className='bg-yellow-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                        <FaBalanceScaleLeft className=' font-bold' />
                    </button>
                </div> : <></>
            }
            {
                (row.offer_status == 'offer_accepted' || row.offer_status == 'counter_offer') ? <>
                    <div className='has-tooltip'>
                        <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-green-600 px-1 -mt-8'>Pay Now</span>
                        <button onClick={() => { handlePopUp(row, 'pay') }} className='bg-green-600 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                            <PiCurrencyDollarDuotone className=' font-bold' />
                        </button>
                    </div> 
                    <div className='has-tooltip'>
                        <span className='tooltip rounded shadow-lg p-1 bg-gray-100 text-red-600 px-1 -mt-8'>Close Deal</span>
                        <button onClick={() => { handlePopUp(row, 'close') }} className='bg-red-700 rounded-md font-bold text-lg text-white p-1.5 flex items-center space-x-2'>
                            <ImCross className=' font-bold' />
                        </button>
                    </div> 
                </>
                : <></>
            }
        </div>
    }

    const getStatus = (status) => {
        return <span className={`${statusColors[status]} rounded-full p-1 px-3 text-xs font-bold capitalize`}>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
        // if (status == 'paid_transfer_completed') return <span className='text-green-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
        // else if (status == 'waiting') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status}</span>
        // else if (status == 'offer_accepted') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
        // else if (status == 'counter_offer') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ')}</span>
        // else if (status == 'user_counter_offer') return <span className='text-yellow-500   1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ')}</span>
        // else if (status == 'paid') return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>Offer Paid</span>
        // else return <span className='text-yellow-500   p-1 rounded-full px-3 text-xs font-bold capitalize'>{status.replaceAll('_', ' ').replaceAll('offer', '')}</span>
    }

    const customStyles = {
        headCells: {
            style: {
                color: "#111111",
                fontSize: '15px',
            },
        },
        rows: {
            style: {
                minHeight: '34px',
                fontSize: '14px',
                '&:not(:last-of-type)': {
                    borderBottomStyle: 'solid',
                    borderBottomWidth: '1px',
                    borderBottomColor: 'rgb(243 244 246 / 1)',
                },
            },
            highlightOnHoverStyle: {
                borderBottomColor: '#FFFFFF',
                outline: '1px solid #FFFFFF',
                background: '#d5e4ea'
            },
        },
        pagination: {
            style: {
                border: 'none',
            },
        },
    };

    useEffect(() => {
        setOffers(myoffers)
    }, [])

    return (
        <UserLayout>
            <div className="mx-auto container max-w-[1200px] mt-20 flex flex-col space-y-4">

                <OfferHistoryPopup getStatus={getStatus} showModal={showModal} modal={modal} domain={domain} />
                <CounterOfferPopup showModal={showCOModal} modal={comodal} domain={domain} offers={offers} setOffers={setOffers} />
                <ConfirmPopUp showModal={showCModal} status={status} offers={offers} setOffers={setOffers} modal={cmodal} domain={domain} />

                <div className="mt-1">
                    <DataTable
                        columns={columns}
                        data={offers}
                        pagination
                        persistTableHead
                        highlightOnHover
                        customStyles={customStyles}
                        pointerOnHover
                        fixedHeader
                        fixedHeaderScrollHeight="600px"
                    />
                </div>
            </div>
        </UserLayout>
    )
}
/*

*/
