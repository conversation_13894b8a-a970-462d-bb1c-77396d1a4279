<?php

namespace App\Modules\Epp\Services;

use App\Exceptions\InsufficientBalanceException;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Payment\Constants\PaymentFees;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Traits\CursorPaginate;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Validator;
use Illuminate\Support\Facades\Validator as ValidatorFacade;;

use stdClass;

class RegistryAccountBalanceService
{
    use CursorPaginate;

    private static $pageLimit = 50;

    private static $minimum_balance;

    public static function getMinimumBalance(): float
    {
        return floatval(Settings::instance()->getValueByKey(SettingKey::MINIMUM_REGISTRY_BALANCE));
    }

    public static function allRegistry(): Collection
    {
        return DB::table('registries')->get();
    }

    public static function debit(stdClass $balance, float $amount, string $transactionType, string $description): Validator|bool
    {
        $validator = ValidatorFacade::make(['balance' => $balance->balance, 'amount' => $amount], [
            'balance' => 'required|numeric',
            'amount' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $validator;
        }

        $afterDeduction = self::fillTransactionDetail($balance, $transactionType, $description);
        $afterDeduction['balance'] = $balance->balance + $amount;
        $afterDeduction['debit'] = $amount;
        $afterDeduction['credit'] = 0;

        return DB::table('registry_account_balances')->insert($afterDeduction);
    }

    public static function credit(stdClass $balance, float $amount, string $transactionType, string $description): Validator|bool
    {
        // app(AuthLogger::class)->info('credit balance'.json_encode($balance));
        // app(AuthLogger::class)->info('credit balance'.json_encode($amount));

        $validator = ValidatorFacade::make(['balance' => $balance->balance, 'amount' => $amount], [
            'balance' => 'required|numeric',
            'amount' => 'required|numeric|min:0|lte:balance',
        ]);

        if ($validator->fails()) {
            return $validator;
        }

        $afterDeduction = self::fillTransactionDetail($balance, $transactionType, $description);
        $afterDeduction['balance'] = $balance->balance - $amount;
        $afterDeduction['debit'] = 0;
        $afterDeduction['credit'] = $amount;

        // app(AuthLogger::class)->info('Registry account balance credit after deduction'.json_encode($afterDeduction));

        return DB::table('registry_account_balances')->insert($afterDeduction);
    }

    public static function setBalanceToZero(int $registryId, string $transactionType = RegistryTransactionType::ZERO_BALANCE, string $description = RegistryTransactionType::BILLING_FAILURE): bool
    {
        $balance = self::balance($registryId);

        $afterDeduction = self::fillTransactionDetail($balance, $transactionType, $description);
        $afterDeduction['balance'] = 0;
        $afterDeduction['debit'] = 0;
        $afterDeduction['credit'] = 0;

        return DB::table('registry_account_balances')->insert($afterDeduction);
    }

    public static function balance(int $registryId): stdClass
    {
        $balance = DB::table('registry_account_balances')->where('registry_id', $registryId)->orderBy('id', 'desc')->first();

        if (! $balance) {
            throw new \Exception('registry id does not exists');
        }

        return $balance;
    }

    public static function checkRegistryBalance(array $domains, array $fee, string $type, string $intent = ""): array
    {
        $totalRegistryAmount = self::getTotalRegistryAmount($domains, $fee, $type);
        $account = self::checkFunds($totalRegistryAmount);

        if ($account['hasInsufficientFund']) {
            if (!empty($intent)) {
                PaymentIntentProvider::instance()->cancelIntent($intent);
            }

            $user = Auth::user();
            $userInfo = $user ? "User ID: {$user->id}, Email: {$user->email}" : "Guest User";
            $domainCount = count($domains);

            $domainNames = [];
            foreach ($domains as $domain) {
                if (is_array($domain) && isset($domain['domain_name'])) {
                    $domainNames[] = $domain['domain_name'];
                } elseif (is_array($domain) && isset($domain['name'])) {
                    $domainNames[] = $domain['name'];
                } elseif (is_object($domain) && property_exists($domain, 'domain_name')) {
                    $domainNames[] = $domain->domain_name;
                } elseif (is_object($domain) && property_exists($domain, 'name')) {
                    $domainNames[] = $domain->name;
                } elseif (is_string($domain)) {
                    $domainNames[] = $domain;
                }
            }
            $domainList = !empty($domainNames) ? implode(', ', $domainNames) : 'Unknown domains';

            $logMessage = "INSUFFICIENT BALANCE ATTEMPT - {$userInfo} | Transaction Type: {$type} | Domains ({$domainCount}): {$domainList} | Required Amount: $" . json_encode($totalRegistryAmount);

            app(AuthLogger::class)->info($logMessage);

            //return error flag
            return [
                'error' => true,
                'message' => 'Registry Account Credit has insufficient balance to complete this transaction.',
                'type' => 'insufficient_balance'
            ];
        }

        return [
            'error' => false,
            'registryBalance' => $account['registryBalance']
        ];
    }

    // PRIVATE Functions

    private static function fillTransactionDetail(stdClass $balance, string $transactionType, string $description): array
    {
        $afterDeduction = [];
        $afterDeduction['previous_id'] = $balance->id;
        $afterDeduction['registry_id'] = $balance->registry_id;
        $afterDeduction['type'] = $transactionType;
        $afterDeduction['description'] = $description;
        $afterDeduction['updated_at'] = now();
        $afterDeduction['created_at'] = now();

        return $afterDeduction;
    }

    private static function getBalances(array $registryIds): array
    {
        // Optimize query with proper indexing approach
        $balance = DB::table('registry_account_balances as rab1')
            ->select('rab1.*')
            ->leftJoin('registry_account_balances as rab2', function($join) {
                $join->on('rab1.registry_id', '=', 'rab2.registry_id')
                     ->whereRaw('rab1.id < rab2.id');
            })
            ->whereIn('rab1.registry_id', $registryIds)
            ->whereNull('rab2.id')
            ->get()
            ->keyBy('registry_id')
            ->all();

        if (! $balance) {
            throw new \Exception('registry id does not exist');
        }

        return $balance;
    }

    private static function checkFunds(array $totalAmountPerRegistry): array
    {
        $hasInsufficientFund = false;
        $registryBalance = [];
        $registryIds = array_keys($totalAmountPerRegistry);
        $balances = self::getBalances($registryIds);

        foreach ($totalAmountPerRegistry as $registryId => $amount) {
            $balance = $balances[$registryId];
            $registryBalance[$registryId] = ['balance' => $balance, 'amount' => $amount];

            if ($amount > $balance->balance) {
                $hasInsufficientFund = true;
                self::updateBalanceFlag($balance, true);
            }
        }

        return ['hasInsufficientFund' => $hasInsufficientFund, 'registryBalance' => $registryBalance];
    }

    private static function getTotalRegistryAmount(array $domains, array $fee, string $type): array
    {
        $feeTypes = PaymentFees::TYPE[$type];
        $tldsInfo = self::getTldsInfo();
        $totalRegistryAmount = [];

        self::calculateTotalRegistryAmount($totalRegistryAmount, $domains, $feeTypes, $tldsInfo, $fee);

        return $totalRegistryAmount;
    }

    private static function calculateTotalRegistryAmount(&$totalRegistryAmount, array $domains, string $feeTypes, array $tldsInfo, array $fee): void
    {
        foreach ($domains as $domain) {
            $domainObject = (object) $domain;
            $tldInfo = $tldsInfo[$domainObject->tld_id];
            $registryId = $tldInfo->registry_id;
            $extName = $tldInfo->name;
            $price = match (true) {
                is_array($fee[$feeTypes][$extName]) => $fee[$feeTypes][$extName]['price'] ?? 0,
                is_object($fee[$feeTypes][$extName]) => $fee[$feeTypes][$extName]->price ?? 0,
                default => 0,
            };

            $domainObject->year_length = (property_exists($domainObject, 'year_length')) ? $domainObject->year_length : 1;
            $amount = $domainObject->year_length * ($price + $fee['icann_fee']);
            $totalRegistryAmount[$registryId] = ($totalRegistryAmount[$registryId] ?? 0) + $amount;
        }
    }

    private static function getTldsInfo(): array
    {
        return DB::table('tlds')
            ->join('extensions', 'extensions.id', '=', 'tlds.extension_id')
            ->get()->keyBy('id')->toArray();
    }

    private static function updateBalanceFlag(stdClass $account, bool $is_flag = true): void
    {
        DB::table('registry_account_balances')
            ->where('id', $account->id)
            ->where('registry_id', $account->registry_id)
            ->update(['is_flag' => $is_flag]);
    }
}