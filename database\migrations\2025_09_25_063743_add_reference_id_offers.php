<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('afternic_offers', function (Blueprint $table) {
            $table->boolean('is_renewal')->default(true)->after('is_refunded');
        });

        if (!Schema::hasColumn('afternic_offers', 'reference_id')) {
            Schema::table('afternic_offers', function (Blueprint $table) {
                $table->string('reference_id')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
