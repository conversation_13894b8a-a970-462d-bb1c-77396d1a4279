<?php

namespace App\Modules\PaymentSummary\Services;

use App\Events\EmailSent;
use App\Exceptions\FailedRequestException;
use App\Mail\Constants\MailConstant;
use App\Mail\Payment\PaymentSummaryMail;
use App\Models\User;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Payment\Services\MultiPaymentInvoiceService;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Util\Constant\QueueConnection;
use App\Util\Helper\CryptHelper;
use Exception;
use Illuminate\Support\Facades\Mail;

class SendMailSummaryService
{
    private int $summaryId;

    private int $userId;

    private array $invoiceData;

    private array $mailPayload;

    public static function instance(): self
    {
        $sendMailSummaryService = new self;

        return $sendMailSummaryService;
    }

    public function handle(int $summaryId, int $userId)
    {
        $this->summaryId = $summaryId;
        // app(AuthLogger::class)->info('invoiceId: ' . json_encode($this->invoiceId));
        $this->userId = $userId;
        // app(AuthLogger::class)->info('userId: ' . json_encode($this->userId));
        $this->invoiceData = $this->getInvoiceData();
        // app(AuthLogger::class)->info('invoiceData: ' . json_encode($this->invoiceData));
        $this->mailPayload = $this->getMailPayload();
        // app(AuthLogger::class)->info('mailPayload: ' . json_encode($this->mailPayload));

        $this->sendMail();
        $this->trackEmailSent();
    }

    // PRIVATE FUNCTIONS

    private function getInvoiceData()
    {
        $summary = ViewPaymentSummaryService::instance()->getById($this->summaryId, $this->userId);
        $data = $this->getInvoiceByType($summary);

        if (array_key_exists('data', $data) && empty($data['data'])) {
            throw new FailedRequestException(404, 'Nothing to process.', 'Data empty');
        }

        $summary->transaction_id = ($summary->transaction_id) ? CryptHelper::decrypt($summary->transaction_id) : '';
        $data['summaryData'] = $summary;
        $data['paid_amount'] = $summary->paid_amount ?? 0;

        return $data;
    }

    private function getInvoiceByType(object $summary)
    {
        if (! $summary) {
            return [];
        }

        $data = [];

        switch ($summary->type) {
            case PaymentSummaryType::MULTI_CHECKOUT_INVOICE:
                $data = MultiPaymentInvoiceService::instance()->getDataBySummaryId($this->summaryId, $this->userId);
                break;
            case PaymentSummaryType::PAYMENT_INVOICE:
                $data = PaymentInvoiceService::instance()->getInvoiceData($summary->payment_invoice_id, $this->userId);
                break;
            default:
                throw new FailedRequestException(404, 'Nothing to process.', 'Data empty');
        }

        return $data;
    }

    private function getMailPayload(): array
    {
        $user = User::findOrFail($this->userId);
        $email = $user->email;
        $name = $user->first_name.' '.$user->last_name;
        $node_type = $this->invoiceData['node_type'] ?? 'default_node_type';

        $payload =
            [
                'data' => $this->invoiceData,
                'name' => $name,
            ];

        return [
            'payload' => $payload,
            'email' => $email,
            'name' => $name,
            'node_type' => $node_type,
        ];
    }

    private function sendMail()
    {
        $payload = $this->mailPayload['payload'];
        $email = $this->mailPayload['email'];

        try 
        {
            app(AuthLogger::class)->info('SendMailSummaryService: Attempting to Send Email');

            $queueMessage = (new PaymentSummaryMail(['payload' => $payload]))
                ->onConnection(QueueConnection::MAIL_JOB)
                ->onQueue(MailConstant::PAYMENT_INVOICE);

            Mail::to($email)->send($queueMessage);
        } 
        catch (Exception $e) 
        {
            app(AuthLogger::class)->error('SendMailSummaryService Error: '.$e->getMessage());
            throw new Exception($e->getMessage());
        }
    }

    private function trackEmailSent()
    {
        $payload = $this->mailPayload['payload'];
        $email = $this->mailPayload['email'];
        $name = $this->mailPayload['name'];
        $node_type = $this->mailPayload['node_type'];

        try {
            $payloadString = json_encode($payload);
            event(new EmailSent(
                $this->userId,
                $name,
                $email,
                'Payment Invoice from '.config('app.name'),
                ucwords(strtolower($node_type)).' Payment Invoice',
                $payloadString,
                null
            ));
        } catch (Exception $e) {
            app(AuthLogger::class)->error('SendMailSummaryService: '.$e->getMessage());
            throw new Exception($e->getMessage());
        }
    }
}
