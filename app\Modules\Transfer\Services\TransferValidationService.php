<?php

namespace App\Modules\Transfer\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TransferValidationService
{
    public static function instance(): self
    {
        return new self;
    }

    public function hasCancellationRequest(int $registeredDomainId): bool
    {
        $cancellationRequest = $this->getCancellationRequestStatus($registeredDomainId);

        if (!$cancellationRequest) {
            return false;
        }

        return $this->shouldRejectDueToCancellation($cancellationRequest);
    }

    public function hasExpiredDomain(int $registeredDomainId): bool
    {
        $expiredDomain = $this->getExpiredTransferDomains($registeredDomainId);

        return $expiredDomain !== null;
    }

    private function getExpiredTransferDomains(int $registeredDomainId): ?object
    {
        $maxExpiredDate = Carbon::now()->subDays(46)->timestamp;
        $minExpiredDate = Carbon::now()->subDays(44)->timestamp;

        return DB::table('registered_domains')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('registered_domains.id', $registeredDomainId)
            ->whereBetween('domains.expiry', [$maxExpiredDate, $minExpiredDate])
            ->first(['domains.expiry']);
    }

    private function getCancellationRequestStatus(int $registeredDomainId): ?object
    {
        return DB::table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->first(['deleted_at', 'feedback_date']);
    }

    private function shouldRejectDueToCancellation(?object $cancellationRequest): bool
    {
        if (!$cancellationRequest) {
            return false;
        }

        $deletedAtIsNull = $cancellationRequest->deleted_at === null;
        $feedbackDateIsNull = $cancellationRequest->feedback_date === null;
        $deletedAtIsNotNull = $cancellationRequest->deleted_at !== null;
        $feedbackDateIsNotNull = $cancellationRequest->feedback_date !== null;

        $pendingDomains = $deletedAtIsNull && $feedbackDateIsNull;
        $approvedDomains = $deletedAtIsNotNull && $feedbackDateIsNotNull;

        return $approvedDomains || $pendingDomains;
    }

}
