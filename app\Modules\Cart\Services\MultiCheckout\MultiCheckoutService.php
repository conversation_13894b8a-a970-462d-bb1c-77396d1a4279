<?php

namespace App\Modules\Cart\Services\MultiCheckout;

use App\Models\MarketCart;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\CreateServices\CreatedDomains;
use App\Modules\Domain\Services\CreateServices\CreateDomainService;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\MarketPlace\Jobs\AfternicDomainHold;
use App\Modules\MarketPlace\Services\MarketCartService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\MultiCheckoutPaymentService;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\RegisteredDomain\Services\DomainRegistrationService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Traits\UserContact;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class MultiCheckoutService
{
    use UserContact;

    private $dispatchDelayInSeconds = 180; // three minutes

    private $requestRegisterDomainsKey = 'domains';

    private $requestMarketDomainsKey = 'market_domains';

    public static function instance(): self
    {
        $multiCheckoutService = new self;

        return $multiCheckoutService;
    }

    public function store(array $request)
    {
        StripeLimiter::instance()->clearAttempt();

        $this->checkMultiRegistryBalance($request);
        $registeredDomains = $this->createRegisteredDomains();
        $marketDomains     = $this->createMarketPlaceDomains();
        $summaryId         = MultiCheckoutPaymentService::instance()->createPaymentSummary($request, $registeredDomains, $marketDomains);

        if ($registeredDomains) {
            $this->callRegistrationJob($request['payment_service_type'], $summaryId, $registeredDomains);
        }

        if ($marketDomains) {
            $this->callMarketRequestJob($marketDomains, $request['payment_service_type']);
        }

        ScheduleDomainExpiryNotice::dispatch($this->getUserId())->delay($this->dispatchDelayInSeconds);

        return $summaryId ?? 0;
    }

    public function checkMultiRegistryBalance(array $request)
    {
        $domains = $request['domains']['domains'] ?? [];
        $marketDomains = $request['market_domains']['domains'] ?? [];
        $intent = $request['intent'] ?? '';

        if (! empty($domains)) {
            $domainBalance = $this->checkRegistryBalance($domains, $request['domains']['other_fees'], FeeType::REGISTRATION, $intent);
            if ($domainBalance['error']) {
                throw new \Exception($domainBalance['message']);
            }
            $this->creditMultiRegistryBalance($domainBalance['registryBalance'] ?? [], []);
        }

        if (! empty($marketDomains)) {
            $marketBalance = $this->checkRegistryBalance($marketDomains, $request['market_domains']['other_fees'], FeeType::TRANSFER, $intent);
            if ($marketBalance['error']) {
                throw new \Exception($marketBalance['message']);
            }
            $this->creditMultiRegistryBalance([], $marketBalance['registryBalance'] ?? []);
        }
    }

    public function getCreatedDomains(Collection $cartContent, $status = UserDomainStatus::RESERVED)
    {
        $domains = new CreatedDomains($cartContent);
        $createdDomains = $domains->format()->store()->getDomains();
        $unregisteredDomains = $this->addVendorsToCreatedDomains($cartContent, $createdDomains);

        $domainService = new DomainRegistrationService($this->getUserId());
        $registeredDomains = $domainService->store($unregisteredDomains, $status);

        return $registeredDomains;
    }

    // PRIVATE FUNCTIONS

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function checkRegistryBalance(array $domains, array $fees, string $type, string $intent): array
    {
        return RegistryAccountBalanceService::checkRegistryBalance($domains, $fees, $type, $intent);
    }

    private function creditMultiRegistryBalance(array $domainBalance, array $marketBalance)
    {
        if (! empty($domainBalance)) {
            $this->creditRegistryAccountBalance($domainBalance, RegistryTransactionType::SUB_FUND, RegistryTransactionType::DOMAIN_REGISTRATION);
        }

        if (! empty($marketBalance)) {
            $this->creditRegistryAccountBalance($marketBalance, RegistryTransactionType::SUB_FUND, RegistryTransactionType::MARKETPLACE_TRANSFER);
        }
    }

    private function creditRegistryAccountBalance(array $registryBalance, $transactionType, $description): void
    {
        foreach ($registryBalance as $balance) {
            RegistryAccountBalanceService::credit(
                $balance['balance'],
                $balance['amount'],
                $transactionType,
                $description
            );
        }
    }

    private function createRegisteredDomains()
    {
        $insertedDomains = CreateDomainService::instance()->storeDomains();
        if (! $insertedDomains) {
            return null;
        }

        return CreateDomainService::instance()->storeRegisteredDomains($insertedDomains, UserDomainStatus::OWNED);
    }

    private function createMarketPlaceDomains()
    {
        $cartContent = MarketCartService::instance()->get();
        if ($cartContent->isEmpty()) {
            return null;
        }
        $createdDomains = $this->getCreatedDomains($cartContent, UserDomainStatus::RESERVED);
        $this->removeFromCart($cartContent);

        return $createdDomains;
    }

    private function addVendorsToCreatedDomains(Collection $cartContent, Collection $createdDomains)
    {
        $cartNames = $this->getVendorsFromCart($cartContent);

        foreach ($createdDomains as &$domain) {
            if (isset($cartNames[$domain->name])) {
                $domain->vendor = $cartNames[$domain->name]->vendor ?? '';
                $domain->price = $cartNames[$domain->name]->price ?? 0;
            }
        }

        return $createdDomains;
    }

    private function getVendorsFromCart(Collection $cartContent)
    {
        $content = [];
        foreach ($cartContent as $item) {
            $content[$item->name] = $item;
        }

        return $content;
    }

    private function removeFromCart(Collection $marketCarts): void
    {
        $ids = $marketCarts->pluck('id')->toArray();

        MarketCart::whereIn('id', $ids)
            ->where('user_id', $this->getUserId())->delete();
    }

    private function callRegistrationJob(string $paymentServiceType, int $summaryId, array $createdDomains)
    {
        $summaryData = PaymentSummaryService::instance()->getById($summaryId, $this->getUserId());
        $refundDetails = PaymentReimbursementService::instance()->createRefundDetails(
            PaymentSummaryType::PAYMENT_REIMBURSEMENT,
            $paymentServiceType,
            $summaryData->payment_invoice_id,
        );
        CreateDomainService::instance()->registerToEpp($createdDomains['domains'], $refundDetails);
    }

    private function callMarketRequestJob(?Collection $marketDomains, string $paymentServiceType)
    {
        // hold afternic domains
        // get order id
        foreach ($marketDomains as $domain) {
            AfternicDomainHold::dispatch(
                $domain,
                $this->getUserId(),
                'market',
                'null',
                $paymentServiceType
            );
        }
    }
}
