<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_payment_methods', function (Blueprint $table) {
            $table->boolean('is_disabled')->default(false)->after('is_default');
            $table->timestamp('disabled_at')->nullable()->after('is_disabled');
            $table->integer('failed_attempts')->default(0)->after('disabled_at');
            $table->timestamp('last_failure_at')->nullable()->after('failed_attempts');
            $table->text('failure_reason')->nullable()->after('last_failure_at');

            $table->index(['is_disabled', 'disabled_at']);
            $table->index(['failed_attempts', 'last_failure_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_payment_methods', function (Blueprint $table) {
            $table->dropIndex(['is_disabled', 'disabled_at']);
            $table->dropIndex(['failed_attempts', 'last_failure_at']);

            $table->dropColumn([
                'is_disabled',
                'disabled_at',
                'failed_attempts',
                'last_failure_at',
                'failure_reason'
            ]);
        });
    }
};
