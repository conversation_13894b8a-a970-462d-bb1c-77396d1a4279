<?php

namespace App\Console\Commands\AccountBalance;

use App\Events\Payment\DepositBankTransferEvent;
use App\Modules\AccountCredit\Constants\AccountCreditSourceType;
use App\Modules\BankTransfer\Constants\BankTransferStatus;
use App\Modules\BankTransfer\Services\BankTransferMailService;
use App\Modules\BankTransfer\Services\BankTransferService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Notification\Services\AccountCreditNotificationService;
use Exception;
use Illuminate\Console\Command;

class AccountCreditor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'account-balance:account-creditor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'checking verified transactions...';

    private $logName = 'AccountCreditor: ';

    private $size = 10;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = $this->logName.$e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info($this->logName.'Running...');

        $this->getVerifiedBankTransfers();
        $this->mailReviewedBankTransfers();
        $this->mailRejectedBankTransfers();

        app(AuthLogger::class)->info($this->logName.'Done');
    }

    private function getVerifiedBankTransfers()
    {
        $bankTransfers = BankTransferService::instance()->getVerifiedBankTransfersToBeDebited($this->size);
        $bankTransfersIds = $bankTransfers->pluck('id')->toArray();
        BankTransferService::instance()->updateRetrievedTransfer($bankTransfersIds);

        foreach ($bankTransfers as $bankTransfer) {
            // Send email notification for verified transfer
            $payload = [
                'email' => $bankTransfer->email,
                'name' => $bankTransfer->first_name.' '.$bankTransfer->last_name,
                'user_id' => $bankTransfer->user_id,
                'bank_transfer' => $bankTransfer,
                'type' => BankTransferStatus::VERIFIED,
                'subject' => 'Your Bank Transfer Request has been Approved - '.config('app.name'),
            ];

            BankTransferMailService::instance()->sendMail($payload);

            // Send system notification for verified transfer
            AccountCreditNotificationService::instance()->sendBankTransferApprovedNotif([
                'user_id' => $bankTransfer->user_id,
                'amount' => $bankTransfer->net_amount,
            ]);

            event(new DepositBankTransferEvent(
                $bankTransfer->id,
                $bankTransfer->payment_service_id,
                $bankTransfer->user_id,
                AccountCreditSourceType::BANK_TRANSFER,
            ));
        }
    }

    private function mailReviewedBankTransfers()
    {
        $reviewedBankTransfers = BankTransferService::instance()->getReviewedTransfers($this->size);
        $bankTransfersIds = $reviewedBankTransfers->pluck('id')->toArray();
        BankTransferService::instance()->updateRetrievedTransfer($bankTransfersIds);

        foreach ($reviewedBankTransfers as $bankTransfer) {
            $payload = [
                'email' => $bankTransfer->email,
                'name' => $bankTransfer->first_name.' '.$bankTransfer->last_name,
                'user_id' => $bankTransfer->user_id,
                'bank_transfer' => $bankTransfer,
                'type' => BankTransferStatus::UNVERIFIED,
                'subject' => 'Contact Support for Your Bank Transfer Request - '.config('app.name'),
            ];

            BankTransferMailService::instance()->sendMail($payload);
        }
    }

    private function mailRejectedBankTransfers()
    {
        $rejectedBankTransfers = BankTransferService::instance()->getRejectedTransfers($this->size);
        $bankTransfersIds = $rejectedBankTransfers->pluck('id')->toArray();
        BankTransferService::instance()->updateRetrievedTransfer($bankTransfersIds);

        foreach ($rejectedBankTransfers as $bankTransfer) {
            $payload = [
                'email' => $bankTransfer->email,
                'name' => $bankTransfer->first_name.' '.$bankTransfer->last_name,
                'user_id' => $bankTransfer->user_id,
                'bank_transfer' => $bankTransfer,
                'type' => BankTransferStatus::REJECTED,
                'subject' => 'Your Bank Transfer Request has been Rejected - '.config('app.name'),
            ];

            BankTransferMailService::instance()->sendMail($payload);

            // Send system notification for rejected transfer
            AccountCreditNotificationService::instance()->sendBankTransferRejectedNotif([
                'user_id' => $bankTransfer->user_id,
                'amount' => $bankTransfer->gross_amount,
                'reason' => 'Bank transfer verification failed',
            ]);
        }
    }
}
