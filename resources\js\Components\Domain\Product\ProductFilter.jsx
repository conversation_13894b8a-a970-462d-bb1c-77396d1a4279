import React, { useEffect, useState } from "react";
import Filter from "@/Components/Domain/Product/Filter";
import { MdOutlineFilterAlt } from "react-icons/md";
import SearchDomainFilter from "@/Components/Domain/Product/SearchDomainFilter";
import { router } from "@inertiajs/react";
import _Product from "@/Constant/_Product";


export default function ProductFilter({
    status_type,
    items,
}) {
    const params = route().params
    const { orderby, status, tld, domain } = params;
    const [hasDomainSearch, setHasDomainSearch] = useState(!!domain);
    const [domainSearchValue, setDomainSearchValue] = useState(domain || "");

    const convertToTitleCase = (e) => {
        const str = e.toLowerCase();
        const words = str.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1));
        return words.join(" ");
    }

    const getStatusKeys = (statusArray) => {
        return Object.values(statusArray).map((e) => convertToTitleCase(e));
    };

    const [filter, setFilter] = useState({
        container: {
            active: false,
            reload: false,
        },
        field: {
            orderby: {
                active: false,
                value: orderby ? [orderby] : [],
                type: "option",
                items: Object.values(_Product.SORT_TYPE),
                name: "Order By",
            },
            status: {
                active: false,
                value: status ? [status] : [],
                type: "option",
                items: getStatusKeys(status_type),
                name: "Status",
            },
            tld: {
                active: false,
                value: tld ? [tld] : [],
                type: "option",
                items: ["com", "net", "org"],
                name: "TLD",
            },
        },
    });

    const doHideFilter = () => {
        const paramNames = Object.keys(params);
        return items.length === 0 && (paramNames.length === 0 || (paramNames.length === 1 && paramNames.includes('transaction')));
    }

    return (
        <div className={`flex justify-between items-center flex-wrap gap-2 ${doHideFilter() && "opacity-0 select-none pointer-events-none"} `}>
            <div className="flex flex-wrap items-center gap-2">
                <label className="flex items-center text-sm text-gray-600">
                    <MdOutlineFilterAlt />
                    <span className="ml-2">Filter:</span>
                </label>
                <Filter filter={filter} setFilter={setFilter} />
            </div>

            <div className="flex items-center justify-center gap-x-2">
                <SearchDomainFilter
                    hasDomainSearch={hasDomainSearch}
                    setHasDomainSearch={setHasDomainSearch}
                    setDomainSearchValue={setDomainSearchValue}
                    searchedDomain={domainSearchValue}
                />
            </div>
        </div>
    );
}