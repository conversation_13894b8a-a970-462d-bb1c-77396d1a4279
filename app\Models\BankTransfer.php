<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankTransfer extends Model
{
    /** @use HasFactory<\Database\Factories\BankTransferFactory> */
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'account_name',
        'gross_amount',
        'net_amount',
        'service_fee',
        'note',
        'verified_at',
        'company',
        'purpose'
    ];
}
