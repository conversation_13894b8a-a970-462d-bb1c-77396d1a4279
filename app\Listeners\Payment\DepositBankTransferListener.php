<?php

namespace App\Listeners\Payment;

use App\Events\Payment\DepositBankTransferEvent;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\AccountCredit\Services\DepositAccountCreditService;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Notification\Services\AccountCreditNotificationService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class DepositBankTransferListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(DepositBankTransferEvent $event)
    {
        try {
            app(AuthLogger::class)->info("DepositBankTransferListener: Processing wire transfer for user {$event->userId}, source {$event->sourceId}");

            // Create the payment
            DepositAccountCreditService::instance()->createPayment(
                [
                    'source_id' => $event->sourceId,
                    'payment_service_id' => $event->paymentServiceId,
                ],
                $event->userId,
                PaymentSummaryType::ACCOUNT_BALANCE,
                $event->sourceType,
            );

            // Get updated account balance and send notification
            $latestBlock = AccountCreditService::instance()->getLatestBlock($event->userId);

            if ($latestBlock) {
                AccountCreditNotificationService::instance()->sendAccountDebitSuccessNotif([
                    'user_id' => $event->userId,
                    'amount' => $latestBlock->amount,
                    'balance' => $latestBlock->running_balance,
                ]);

                app(AuthLogger::class)->info("DepositBankTransferListener: Account credited successfully. New balance: {$latestBlock->running_balance}");
            }

        } catch (Exception $e) {
            app(AuthLogger::class)->error("DepositBankTransferListener: Failed to process wire transfer - " . $e->getMessage());
            throw $e; // Re-throw to ensure the job fails and can be retried
        }
    }
}
