<?php

namespace App\Modules\Domain\Services;

use App\Models\Domain;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Jobs\RequestDomainAuthCode;
use App\Modules\Notification\Services\DomainNotificationService;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DomainService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $domainService = new self;

        return $domainService;
    }

    public function updateDomainNameservers($payload) {
        $domainId = $payload['domain']['id'];
        $nameservers = $payload['domain']['nameservers'];
        
        Domain::findOrFail($domainId)->update([
            'nameservers' => $nameservers
        ]);
        
    }

    public function updateDomainStatus($id, string $status, ?bool $isRenewal = false, ?string $email = null): void
    {
        $updateData = ['status' => $status, 'updated_at' => Carbon::now()];

        if ($isRenewal) $updateData['client_renew_at'] = Carbon::now();

        Domain::when(is_int($id), function ($query) use ($id) {
            return $query->where('id', $id);
        })
            ->when(is_array($id), function ($query) use ($id) {
                return $query->whereIn('id', $id);
            })
            ->update($updateData);

        $log_id = is_int($id) ? $id : implode(',', $id);

        $info = 'Updated domain id ' . $log_id . ' to status ' . $status;
        app(AuthLogger::class)->info($this->fromWho($info, $email));
    }

    public function updateDomainServerRenewAt(array $ids, ?string $email = null): void
    {
        if (empty($ids)) {
            return;
        }

        DB::table('domains')->whereIn('id', $ids)->update([
            'server_renew_at' => DB::raw("(TO_TIMESTAMP(expiry / 1000) AT TIME ZONE 'UTC') + INTERVAL '1 year'"),
            'updated_at' => Carbon::now(),
        ]);

        app(AuthLogger::class)->info($this->fromWho('Updated domain id ' . implode(',', $ids) . ' server_renew_at.', $email));
    }

    public function deleteDomain(int $domainId, string $domainStatus, string $registeredDomainStatus, string $email): void
    {
        $now = Carbon::now();

        DB::table('registered_domains')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('registered_domains.domain_id', $domainId)
            ->update(['status' => $registeredDomainStatus, 'deleted_at' => $now, 'updated_at' => $now]);

        DB::table('domains')->where('id', $domainId)
            ->update(['status' => $domainStatus, 'deleted_at' => $now, 'updated_at' => $now]);

        app(AuthLogger::class)->info($this->fromWho('Deleted domain id ' . $domainId . '.', $email));
    }

    public function sendAuthCode(array $registry_domains, array $domains_for_update, int $maxDomainsPerEmail): void
    {
        RequestDomainAuthCode::dispatch(Auth::user()->email, $registry_domains, $domains_for_update, $this->getUserId(), $maxDomainsPerEmail);

        DomainNotificationService::instance()->sendAuthCodeRequestInProcessNotif();
    }

    public function buildQuery(array $data, int $userId)
    {
        $column = 'domain_id';
        $direction = 'desc';

        $orderBy =
            [
                'column' => $column,
                'direction' => $direction,
            ];

        if (isset($data['filterOrderBy'])) {
            $arrayValues = explode(':', $data['filterOrderBy']);

            if (count($arrayValues) == 2 && in_array($arrayValues[1], ['asc', 'desc'])) {
                $orderBy['direction'] = $arrayValues[1];

                switch ($arrayValues[0]) {
                    case 'created':
                        $orderBy['column'] = 'domains.created_at';
                        break;

                    case 'expiry':
                        $orderBy['column'] = 'domains.expiry';
                        break;

                    case 'name':
                        $orderBy['column'] = 'domains.name';
                        break;
                }
            }
        }

        $selectedColumns =
            [
                'domains.id as id',
                'domains.id as domain_id',
                'domains.name',
                'domains.status',
                'domains.root',
                'domains.root as tld_id',
                'domains.registrant',
                'domains.year_length',
                'domains.created_at',
                'domains.updated_at',
                'domains.expiry',
                'domains.contacts',
                'domains.client_status',
                'domains.hosts',
                'domains.nameservers',
                'domains.auth_code_updated_at',
                'registered_domains.id as registered_domain_id',
                'registered_domains.user_contact_registrar_id',
                'registered_domains.user_category_id',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'registered_domains.user_category_id',
                'registered_domains.locked_until',
                'registered_domains.contacts_id',
                'registered_domains.extension_id',
                'user_contacts.user_id as user_contact_user_id',
                'extensions.name as extension_name',
                'extensions.name as extension',
                'user_categories.name as user_category_name',
                'registries.name as registry',
                'registries.id as registry_id',
            ];

        $query = DB::table('domains')
            ->join('registered_domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('registries', 'registries.id', '=', 'user_contacts.registry_id')
            ->join('extensions', 'extensions.id', '=', 'registered_domains.extension_id')
            ->join('user_categories', 'user_categories.id', '=', 'registered_domains.user_category_id')
            ->whereNull('domains.deleted_at')
            ->where('user_contacts.user_id', '=', $userId)
            ->where('registered_domains.status', '=', UserDomainStatus::OWNED)
            ->select($selectedColumns)
            ->when(
                isset($data['filterSelectedItems']),
                function (Builder $q) use ($data) {
                    $q->whereIn('domain_id', $data['filterSelectedItems']);
                }
            )
            ->when(
                isset($data['filterName']),
                function (Builder $q) use ($data) {
                    $q->where('domains.name', 'ilike', $data['filterName'] . '%');
                }
            )
            ->when(
                isset($data['filterStatus']),
                function (Builder $q) use ($data) {
                    $status = preg_replace('/\s+/', '_', strtoupper($data['filterStatus']));

                    if (! in_array($status, DomainStatus::all())) {
                        return;
                    }

                    $q->where('domains.status', $status);
                }
            )
            ->when(
                isset($data['filterCategory']),
                function (Builder $q) use ($data) {
                    $category = explode(',', $data['filterCategory']);

                    if (empty($category)) {
                        return;
                    }

                    $q->whereIn('user_categories.name', $category);
                }
            )
            ->when(
                isset($data['filterTld']),
                function (Builder $q) use ($data) {
                    $tld = $data['filterTld'];

                    if (! in_array($tld, ['com', 'net', 'org'])) {
                        return;
                    }

                    $q->where('extensions.name', $tld);
                }
            )
            ->when(
                isset($data['filterNameserver']),
                function (Builder $q) use ($data) {
                    $q->where('domains.nameservers', 'ilike', '%' . $data['filterNameserver'] . '%');
                }
            )
            ->orderBy(
                $orderBy['column'],
                $orderBy['direction']
            );

        return $query;
    }

    // ! PRIVATE FUNCTIONS
    private static function getUserId(): int
    {
        return auth()->user()->id ?? 0;
    }

    private static function getUserEmail(): string
    {
        return auth()->user()->email ?? 'Unauthorized';
    }
}
