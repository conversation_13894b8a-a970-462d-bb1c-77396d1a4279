<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop auto_renewal_notification_queue table
        Schema::dropIfExists('auto_renewal_notification_queue');

        // Drop auto_renewal_logs table
        Schema::dropIfExists('auto_renewal_logs');

        // Drop auto_renewal_notifications table
        Schema::dropIfExists('auto_renewal_notifications');

        // Drop jobs table
        Schema::dropIfExists('jobs');

        // Remove columns from domains table
        Schema::table('domains', function (Blueprint $table) {
            $table->dropColumn([
                'auto_renewal_enabled',
                'auto_renewal_attempt_at',
                'auto_renewal_attempts',
                'auto_renewal_failure_reason'
            ]);
        });

        // Remove columns from user_payment_methods table
        Schema::table('user_payment_methods', function (Blueprint $table) {
            $table->dropIndex(['is_disabled', 'disabled_at']);
            $table->dropIndex(['failed_attempts', 'last_failure_at']);
            $table->dropColumn([
                'is_disabled',
                'disabled_at',
                'failed_attempts',
                'last_failure_at',
                'failure_reason'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // -- None --
    }
};