<?php

namespace App\Modules\Domain\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Domain\Requests\Renewal\RenewalConfirmRequest;
use App\Modules\Domain\Requests\Renewal\RenewalDomainRequest;
use App\Modules\Domain\Requests\Renewal\RenewalPayRequest;
use Inertia\Inertia;
use Inertia\Response;

class RenewalController extends Controller
{
    public function confirm(RenewalConfirmRequest $request): Response
    {
        return Inertia::render('Domain/Renewal', $request->getData());
    }

    public function pay(RenewalPayRequest $request): Response
    {
        return Inertia::render('Domain/RenewPayment', $request->getRenewalPaymentData());
    }

    public function renew(RenewalDomainRequest $request)
    {
        try {
            $summaryId = $request->update();

            return Inertia::render('Notice/ConfirmationMessage', [
                'message' => 'Payment Successful. Domain renewal is in process.',
                'redirect' => [['route' => route('payment.summary.view', ['id' => $summaryId]), 'label' => 'Show Payment Invoice']],
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }
}
