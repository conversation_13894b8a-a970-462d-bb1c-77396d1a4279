<?php

namespace App\Modules\Domain\Requests\Product;

use App\Modules\Domain\Services\ViewServices\ViewDomainService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Modules\MarketPlace\Constants\MarketConstants;


class ShowListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'status' => strtolower($this->status)
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'limit' => ['nullable', 'integer', 'max:100'],
            'orderby' => [
                'nullable',
                'string',
                Rule::in([
                    "Created: Asc",
                    "Created: Desc",
                    "Last Status Update: Asc",
                    "Last Status Update: Desc",
                    "Price: Asc",
                    "Price: Desc",
                    "Domain: Asc",
                    "Domain: Desc"
                ])
            ],
            'status' => ['nullable', 'string', Rule::in(MarketConstants::getProductsTab())],
            'tld' => ['nullable', 'string', 'exists:extensions,name'],
            'name' => ['nullable', 'string', 'max:255'],
        ];
    }

    public function getAll(): array
    {
        return ViewDomainService::instance()->getAllProducts($this);
    }
}
