<?php

namespace App\Modules\Domain\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\JobServices\JobRecord;
use App\Modules\Domain\Services\JobServices\JobRenewEppService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class RenewEppDomain implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use UserLoggerTrait;

    private object $domain;

    private object $registeredDomain;

    private int $id;

    private int $userId;

    private string $registry;

    private string $email;

    private $updateType;

    private int $backOffMinutes = 5; // (30 * 60)

    private JobRecord $jobRecord;

    private array $refundData;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    // public function __construct($domain, $userId, $registry, $email)
    public function __construct($payload)
    {
        $this->domain = $payload[JobPayloadKeys::DOMAIN];
        $this->registeredDomain = $payload[JobPayloadKeys::REGISTERED_DOMAIN];
        $this->userId = $payload[JobPayloadKeys::USER_ID];
        $this->registry = $payload[JobPayloadKeys::REGISTRY];
        $this->email = $payload[JobPayloadKeys::EMAIL];
        $this->updateType = $payload[JobPayloadKeys::UPDATE_TYPE] ?? \App\Modules\Domain\Constants\DomainJobTypes::UPDATE_RENEWAL;
        $this->refundData = $payload[JobPayloadKeys::REFUND_DATA] ?? [];

        $this->id = $this->domain->id;
        $this->jobRecord = $this->createRecord();

        $this->onConnection(QueueConnection::DOMAIN_RENEWAL);
        $this->onQueue(QueueTypes::DOMAIN_RENEWAL[$this->registry]);
    }

    public $uniqueFor = 300; // can retry every 5 minutes

    public function uniqueId(): int
    {
        return intval(Carbon::now()->timestamp.$this->id);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            app(AuthLogger::class)->info($this->fromWho('Starting RenewEppDomain job for domain ID: ' . $this->domain->id . ', User ID: ' . $this->userId, $this->email));
            JobRenewEppService::instance()->handle($this->jobRecord);
            app(AuthLogger::class)->info($this->fromWho('Completed RenewEppDomain job for domain ID: ' . $this->domain->id, $this->email));
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho('RenewEppDomain job failed: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ':' . $e->getLine(), $this->email));
            app(AuthLogger::class)->error($this->fromWho('Stack trace: ' . $e->getTraceAsString(), $this->email));

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                app(AuthLogger::class)->info($this->fromWho('Retrying RenewEppDomain job', $this->email));
                $this->retry();
                return;
            }

            app(AuthLogger::class)->error($this->fromWho('Failing RenewEppDomain job permanently', $this->email));
            $this->fail($e);
        }
    }

    public function failed(?Throwable $exception = null): void
    {
        if ($exception) {
            app(AuthLogger::class)->error($this->fromWho('RenewEppDomain job failed in failed() method: ' . $exception->getMessage(), $this->email));
        } else {
            app(AuthLogger::class)->error($this->fromWho('RenewEppDomain job failed in failed() method with no exception', $this->email));
        }

        // Update job record to mark as failed
        if (isset($this->jobRecord)) {
            $this->jobRecord->stopJobRetry(\App\Modules\Domain\Constants\DomainStatus::FAILED);
        }

        // Send user notification of failure, etc...
    }

    public function retry(): void
    {
        $this->jobRecord->setPendingJob();
    }

    private function createRecord()
    {
        return new JobRecord([
            JobPayloadKeys::DOMAIN => $this->domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $this->registeredDomain,
            JobPayloadKeys::REGISTRY => $this->registry,
            JobPayloadKeys::USER_ID => $this->userId,
            JobPayloadKeys::EMAIL => $this->email,
            JobPayloadKeys::UPDATE_TYPE => $this->updateType,
            JobPayloadKeys::QUEUE_TYPE => QueueConnection::DOMAIN_RENEWAL,
            JobPayloadKeys::REFUND_DATA => $this->refundData,
        ]);
    }
}
