//* PACKAGES
import { Suspense, useEffect } from "react";
import { usePage } from "@inertiajs/react";

//* ICONS
import { MdMenu } from "react-icons/md";
import { PiStorefrontDuotone } from "react-icons/pi";
import { TbMessageReport } from "react-icons/tb";
import { MdLogout } from "react-icons/md";

//* COMPONENTS
import { NotificationDropdown } from "../Components/Notification/NotificationDropdown";
import { ProfileDropdown } from "../Components/Profile/ProfileDropdown";
import { CartDropdown } from "../Components/Domain/Cart/CartDropdown";
import { TransferCartDropdown } from "@/Components/Transfer/Cart/CartDropdown";
import AccountCenterNav from "@/Components/Nav/AccountCenterNav";
import AccountSecurityNav from "@/Components/Nav/AccountSecurityNav";
import PreferenceNav from "@/Components/Nav/PreferenceNav";
import PushDomainNav from "@/Components/Nav/PushDomainNav";
import DomainNav from "../Components/Nav/DomainNav";
import DomainTransferNav from "@/Components/Nav/DomainTransferNav";
import SecurityNav from "@/Components/Nav/SecurityNav";
// import { TransferCartDropdown } from "@/Components/Transfer/Cart/CartDropdown";
//import { Suspense, useEffect } from "react";
import CartCounterState from "@/State/CartCounterState";
import TransferCartCounterState from "@/State/TransferCartCounterState";
import SideNavState from "@/State/SideNavState";
import BillingNav from "../Components/Nav/BillingNav";
import { router } from "@inertiajs/react";

//* UTILS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function UserLayout({
    children,
    postRouteName = "",
    setup = true,
    isReportAbusePage,
    additionalMainElementClassNames = "",
    isLimiterPage = false,
}) {
    const { showSideNav, setShowSideNav } = SideNavState();
    const currentRoute = route().current() || postRouteName;
    const { cartCounter, setCartCounter } = CartCounterState();
    const { transferCartCounter, setTransferCartCounter } =
        TransferCartCounterState();

    const user = usePage().props.auth.user;
    const verified = usePage().props.is_verified;
    // const home_url = usePage().props.subdomains.home;

    const backgroundStyle = isReportAbusePage ? "bg-gray-100" : "bg-white";

    const notifications = window.notifications || [];

    useEffect(() => {
        eventListener("MyCartCountEvent", "MyCart", setCartCounter);
        eventListener(
            "MyTransferCartCountEvent",
            "MyTransferCart",
            setTransferCartCounter
        );
    }, [user.id]);

    const handleLogout = (e) => {
        e.preventDefault(); // Prevent the default behavior of the logout link

        console.log("Clearing session storage..."); // For debugging
        sessionStorage.clear(); // Clear session storage

        // Use Inertia's POST method to trigger the logout process
        //router.post(route("category.warn"), {})
        router.post(route("logout", {}), {
            onSuccess: () => toast.success("Logout successfully."),
        });
    };

    const eventListener = (eventName, channelName, countSetter) => {
        window.Echo.private(`${channelName}.${user.id}`).listen(
            eventName,
            (e) => {
                countSetter(e.cartCount);
            }
        );

        return () => {
            channel.stopListening(eventName);
            Echo.leaveChannel(`private-${channelName}.${user.id}`);
        };
    };

    return (
        <div className={`min-h-screen ${backgroundStyle}`}>
            <nav className="bg-primary flex items-center justify-between py-4 px-8 text-white border-b border-gray-400 shadow-md text-3xl">
                {setup && verified ? (
                    <button
                        onClick={() => {
                            setShowSideNav(!showSideNav);
                        }}
                    >
                        <MdMenu
                            className={`hover:bg-black hover:bg-opacity-20 rounded-full text-3xl p-1 ease-in-out duration-300 ${showSideNav
                                ? "transform rotate-0"
                                : "transform rotate-180"
                                }`}
                        />
                    </button>
                ) : (
                    <button className="pointer-events-none" disabled={!setup}>
                        <MdMenu className="opacity-0" />
                    </button>
                )}

                <div className="flex items-center space-x-2 cursor-pointer">
                    {setup && verified && (
                        <>
                            {currentRoute.includes("domain") && (
                                <Suspense>
                                    <CartDropdown />
                                </Suspense>
                            )}
                            {currentRoute.includes("transfer") && (
                                <Suspense>
                                    <TransferCartDropdown />
                                </Suspense>
                            )}
                            <Suspense>
                                <NotificationDropdown />
                            </Suspense>
                        </>
                    )}

                    {isLimiterPage ? (
                        <button
                            onClick={handleLogout}
                            className="text-white hover:bg-black hover:bg-opacity-20 rounded-full p-2 ease-in-out duration-300"
                            title="Logout"
                        >
                            <MdLogout className="text-2xl" />
                        </button>
                    ) : (
                        <ProfileDropdown disabled={!(setup && verified)} />
                    )}
                </div>
            </nav>
            <main
                className={`flex justify-between min-h-[calc(100vh-63px)] ${additionalMainElementClassNames}`}
            >
                {setup && verified && (
                    <aside
                        className={`flex-none bg-white border-r border-gray-200 text-sm ease-in-out duration-300 z-10 min-h-[calc(100vh-63px)] shadow-md overflow-hidden flex flex-col justify-between ${showSideNav ? "w-60" : "w-0"
                            }`}
                    >
                        <div className="flex flex-col pt-6 space-y-4 text-gray-700 font-semibold whitespace-nowrap">
                            <DomainNav postRouteName={postRouteName} />
                            <PreferenceNav postRouteName={postRouteName} />
                            <PushDomainNav postRouteName={postRouteName} />
                            <DomainTransferNav postRouteName={postRouteName} />
                            {/* <AccountCenterNav postRouteName={postRouteName} /> */}
                            {/* <BillingNav postRouteName={postRouteName} /> */}
                            {/* <AccountSecurityNav postRouteName={postRouteName} /> */}

                            {/* <a href={home_url} className="flex items-center space-x-4 hover:shadow-sm pl-8 py-1 hover:text-primary">
                                <span className='flex space-x-4'>
                                    <PiStorefrontDuotone className="text-2xl" />
                                    <span className="text-inherit">
                                        Marketplace
                                    </span>
                                </span>
                            </a> */}
                        </div>
                        <div className="px-9 pb-5">
                            <a
                                href="/abuse"
                                className="flex space-x-4 hover:text-primary"
                            >
                                <span className="flex space-x-4">
                                    <TbMessageReport className="text-2xl" />
                                    <span className="text-inherit">
                                        Report Abuse
                                    </span>
                                </span>
                            </a>
                        </div>
                    </aside>
                )}
                <aside className="container mx-auto px-6 pb-16">
                    {/* <NotificationBanner notifications={notifications} /> */}
                    {children}
                </aside>
            </main>
        </div>
    );
}
