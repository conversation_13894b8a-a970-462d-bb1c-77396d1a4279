<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\DomainHistoryEvent;
use App\Events\UpdateDomainsTableEvent;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainContact;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Domain\Services\ViewServices\DomainBuilder;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\RegisteredDomain\Services\DomainRegistrationService;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\Settings;

class UpdateDomainService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $updateDomainService = new self;

        return $updateDomainService;
    }

    public function updateDomain(array $request)
    {
        $domainRegistrationService = DomainRegistrationService::instance();

        // First update the category
        $domainRegistrationService->updateDomainCategory($request['registered_domain_id'], $request['user_category_id']);

        // Then create the event with the category name
        event(new DomainHistoryEvent([
            'domain_id' => $request['id'],
            'type' => 'DOMAIN_CATEGORY_UPDATED',
            'user_id' => $this->getUserId(),
            'status' => 'success',
            'message' => 'Domain "'.$request['name'].'" category settings updated by '.$this->getUserEmail().' to category "'.$domainRegistrationService->getCategoryName($request['user_category_id']).'".',
            'payload' => $request,
        ]));

        $this->updateEppDomainDetails($request);
    }

    // PRIVATE FUNCTIONS

    private function updateEppDomainDetails(array $request): void
    {
        $payload = $this->createDomainPayload($request);

        $isUpdated = $this->checkUpdate($payload);

        if (! $isUpdated['isUpdated']) {
            app(AuthLogger::class)->info($this->fromWho('Domain details not updated.', $this->getUserEmail()));

            return;
        }

        if ($isUpdated['isNameserverUpdated']) {
            $this->setUpdatedNameservers($payload);
        }

        if ($isUpdated['isContactUpdated']) {
            $payload = $this->addLockedUntil($payload, $request);

        }

        $this->setDomainsToInProcess($request['id']);
        JobDispatchService::instance()->updateEppDispatch($payload);
    }

    private function setUpdatedNameservers($payload) {
        DomainService::instance()->updateDomainNameservers($payload);
    }

    private function setDomainsToInProcess(int $id)
    {
        DomainService::instance()->updateDomainStatus($id, DomainStatus::IN_PROCESS);
        UpdateDomainsTableEvent::dispatch($this->getUserId());
    }

    private function checkUpdate(array $payload)
    {
        $newDomain = (object) $payload['domain'];
        $oldDomain = DomainBuilder::instance()->get($newDomain->id);
        $oldArray = (array) $oldDomain;

        $oldArray['contacts'] = json_decode($oldDomain->contacts, true);
        $oldArray['nameservers'] = json_decode($oldDomain->nameservers, true);
        $updatePayload = new DetailsPayload($newDomain, $oldArray);

        return [
            'isContactUpdated' => $updatePayload->isContactUpdated(),
            'isNameserverUpdated' => $updatePayload->isNameserverUpdated(),
            'isUpdated' => $updatePayload->check(),
        ];
    }

    private function createDomainPayload(array $request): array
    {
        $contactsPayload = $this->getDomainContactPayload($request);
        $nameserversPayload = $this->getDomainNameserversPayload($request);

        $registeredDomain = [
            'id' => $request['registered_domain_id'],
            'name' => $request['name'],
            'user_contact_registrar_id' => $request['registrant_id'],
            'contacts_id' => json_encode($contactsPayload['id']) ?? null,
        ];

        $domain = [
            'id' => $request['id'],
            'name' => $request['name'],
            'registrant' => $request['registrant'],
            'contacts' => $contactsPayload['name'],
            'nameservers' => $nameserversPayload['name'],
        ];

        return [
            JobPayloadKeys::DOMAIN => $domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $registeredDomain,
            JobPayloadKeys::REGISTRY => $request['registry'],
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_DETAILS,
        ];
    }

    private function getDomainContactPayload(array $request): array
    {
        $contactsIdArray = [
            DomainContact::REGISTRANT => $request['registrant_id'],
            DomainContact::ADMIN => $request['admin_contact_id'],
            DomainContact::TECH => $request['technical_contact_id'],
            DomainContact::BILLING => $request['billing_contact_id'],
        ];

        $contactsArray = [
            DomainContact::REGISTRANT => $request['registrant'],
            DomainContact::ADMIN => $request['admin_contact'],
            DomainContact::TECH => $request['technical_contact'],
            DomainContact::BILLING => $request['billing_contact'],
        ];

        return [
            'id' => $contactsIdArray,
            'name' => $contactsArray,
        ];
    }

    private function getDomainNameserversPayload(array $request): array
    {
        $nameserversName = [];
        $nameservers = $request['nameservers'];

        if (empty($nameservers)) {
            return [
                'id' => null,
                'name' => null,
            ];
        }

        // dd($nameservers);

        foreach ($nameservers as $ns) {
            if (is_string($ns) && ! empty(trim($ns))) {
                $nameserversName[] = strtolower($ns);

                continue;
            }
        }

        // event(new DomainHistoryEvent([
        //     'domain_id' => $request['id'],
        //     'type' => 'NAMESERVER_UPDATED',
        //     'user_id' => $this->getUserId(),
        //     'status' => 'success',
        //     'message' => 'Updated nameservers for domain "'.$request['name'].'" to: '.implode(', ', $nameserversName),
        // ]));

        return [
            'name' => $nameserversName,
        ];
    }

    private function getLockedUntil(array $request): int
    {
        $lockinPeriod = intval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_LOCKIN_PERIOD));
        $newLockInDate = now()->addDays($lockinPeriod)->timestamp;
        $isOptIn = $request['is_opt_in'] ? true : false;

        return $isOptIn ? $newLockInDate : $request['locked_until'];
    }

    private function addLockedUntil(array $payload, array $request)
    {
        $isOptIn = $request['is_opt_in'] ? true : false;

        if (! $isOptIn) {
            return $payload;
        }

        $lockedUntil = $this->getLockedUntil($request);

        $registeredDomain = $payload[JobPayloadKeys::REGISTERED_DOMAIN];
        $registeredDomain['locked_until'] = $lockedUntil;

        $domain = $payload[JobPayloadKeys::DOMAIN];
        $domain['status'] = EppDomainStatus::CLIENT_LOCK_STATUS;

        return [
            JobPayloadKeys::DOMAIN => $domain,
            JobPayloadKeys::REGISTERED_DOMAIN => $registeredDomain,
            JobPayloadKeys::REGISTRY => $payload[JobPayloadKeys::REGISTRY],
            JobPayloadKeys::USER_ID => $payload[JobPayloadKeys::USER_ID],
            JobPayloadKeys::EMAIL => $payload[JobPayloadKeys::EMAIL],
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_DETAILS,
        ];
    }

    private function getUserId(): int
    {
        return auth()->user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return auth()->user()->email ?? 'Unauthorized';
    }
}
