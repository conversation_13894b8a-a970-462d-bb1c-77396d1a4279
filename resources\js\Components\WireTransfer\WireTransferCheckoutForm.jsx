//* PACKAGES
import React, {useState, useEffect} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import { useForm } from "@inertiajs/react";

//* ICONS
//...

//* COMPONENTS
import InputLabel from "@/Components/InputLabel";
import TextInput from "@/Components/TextInput";
import InputError from "@/Components/InputError";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS
//...

//* UTILS
//...

//* ENUMS
//...

//* CONSTANTS
import _PaymentSummary from '@/Constant/_PaymentSummary';

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferCheckoutForm(
    {
        type,
        user, 
        domains, 
        marketDomains, 
        otherFees, 
        isActive = false, 
        isDisabled = false, 
        onHandlePageProcessing = () => { },
    }
)
{
    //! PACKAGE
    //... 

    //! HOOKS
    //...
    
    //! VARIABLES
    const getAmount = () =>
    {
        if (!otherFees || typeof otherFees !== 'object')
        {
            console.error('Invalid otherFees object:', otherFees);
            return null;
        }

        if (!('bill_total' in otherFees))
        {
            console.error('bill_total not found in otherFees:', otherFees);
            return null;
        }

        return otherFees.bill_total;
    };

    const amount = getAmount();

    //! STATES
    const [isProcessing, setProcessing]                       = useState(false);
    const [stateInputErrorName, setStateInputErrorName]       = useState(null);
    const [stateInputErrorCompany, setStateInputErrorCompany] = useState(null);

    //! USE EFFECTS
    //...

    //! FUNCTIONS

    const { data, post, setData } = useForm(
        {
            payment_service_type: _PaymentSummary.SERVICE_TYPES.BANK_TRANSFER,
            user_id             : user?.id,
            domains             : domains,
            market_domains      : marketDomains,
            other_fees          : otherFees,
            amount_to_use       : amount,
            bankTransferName    : '', 
            bankTransferCompany : ''
        }
    );

    const handleFieldValidation = () => 
    {
        let error = false; 

        setStateInputErrorName(''); 
        setStateInputErrorCompany('');

        if (data.bankTransferName == '')
        {
            error = true; 

            setStateInputErrorName('Name is required'); 
        }

        if (data.bankTransferCompany == '')
        {
            error = true; 

            setStateInputErrorCompany('Company is required'); 
        }

        return error; 
    }

    const handleSubmit = (e) =>
    {
        e.preventDefault();

        const error = handleFieldValidation(); 

        if (error == true)
        {
            return;
        }

        setProcessing(true);
        onHandlePageProcessing(true);

        let postRoute = null;

        switch (type) {
            case 'renew':
                postRoute = route("domain.renew");
                break;
            case 'transfer':
                postRoute = route("transfer.inbound.checkout");
                break;
            case 'multicheckout':
                postRoute = route("domain.multicart.checkout");
                break;
            // case 'redemption':
            //     postRoute = route("domain-redemption.restore");
            //     break;
            case 'redeem':
                postRoute = route("domain.redeem");
                break;
            case 'offercheckout':
                postRoute = route("marketoffer.checkout.store");
                break;
        }

        if (postRoute)
        {
            post(
                postRoute,
                {
                    replace: true,
                    preserveState: false,
                    onSuccess: () =>
                    {
                        setProcessing(false);
                        onHandlePageProcessing(false);
                    },
                    onError: () =>
                    {
                        setError("There was a problem processing your payment. Please try again.");
                        setProcessing(false);
                        onHandlePageProcessing(false);
                    }
                },
                {
                    replace: true,
                    preserveState: false,
                }
            );
        }
    }
    return (
        <div
            className={`
                bg-white rounded-lg border p-6 transition-all duration-200
                flex flex-col gap-4
                ${isActive ? 'shadow border-primary' : 'border-gray-200'}
                `
            }
        >
            <div className="text-base text-gray-600">
                Bank Transfer
            </div>

            <div
                className="flex flex-col gap-2"
            >
                <div
                    className="flex flex-col gap-2"
                >
                    <InputLabel
                        forInput="name"
                        value="Name"
                    />

                    <TextInput
                        value={data.bankTransferName}
                        type="text"
                        name="name"
                        placeholder="Name"
                        className="block w-full"
                        handleChange={(e) => setData('bankTransferName', e.target.value)}
                    />
                    <InputError
                        message={stateInputErrorName}
                    />
                </div>
                <div
                    className="flex flex-col gap-2"
                >
                    <InputLabel
                        forInput="company"
                        value="Company"
                    />

                    <TextInput
                        value={data.bankTransferCompany}
                        type="text"
                        name="company"
                        placeholder="Company"
                        className="block w-full"
                        handleChange={(e) => setData('bankTransferCompany', e.target.value)}
                    />
                    <InputError
                        message={stateInputErrorCompany}
                    />
                </div>
                {
                    isDisabled == true
                        ?
                            <InputError
                                message={'This option is only available for premium domain transactions.'}
                            />
                        :
                            null
                }
            </div>
            {isActive && (
                <form
                    data-payment-type="wire_transfer"
                    onSubmit={handleSubmit}
                />
            )}
        </div>
    );
}
