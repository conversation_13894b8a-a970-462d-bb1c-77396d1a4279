<?php

namespace App\Exceptions;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Util\Helper\Client\ClientIp;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->renderable(function (MethodNotAllowedHttpException $e, $request) {
            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 405,
                'help' => 'Sorry, the way you access the page is not allowed. It is also possible that your session expires.',
                'error' => 'Method not allowed',
            ]);
        });

        $this->renderable(function (\Illuminate\Auth\AuthenticationException $e, $request) {
            return Inertia::render('Errors/LoginRequiredPage', [
                'code' => 401,
                'help' => 'Please log in to access this page.',
                'error' => 'Authentication Required',
            ]);
        });

        // $this->renderable(function (\Illuminate\Validation\ValidationException $e, $request) {
        //     return Inertia::render('Errors/BackCustomMessage', [
        //         'code' => 422,
        //         'help' => 'Please check your input and try again.',
        //         'error' => 'Validation Error',
        //     ]);
        // });

        $this->renderable(function (\Illuminate\Database\QueryException $e, $request) {
            // Log the actual database error for debugging
            app(\App\Modules\CustomLogger\Services\AuthLogger::class)->error('Database Query Exception: ' . $e->getMessage());

            if (str_contains($e->getMessage(), 'timeout expired')) {
                return Inertia::render('Errors/BackCustomMessage', [
                    'code' => 503,
                    'help' => 'The server is currently experiencing high load. Please try again in a few moments.',
                    'error' => 'Connection Timeout',
                ]);
            }

            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 503,
                'help' => 'The database is temporarily unavailable. Please try again later.',
                'error' => 'Service Unavailable',
            ]);
        });

        $this->renderable(function (\Illuminate\Session\TokenMismatchException $e, $request) {
            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 419,
                'help' => 'Your session has expired. Please refresh the page and try again.',
                'error' => 'Page Expired',
            ]);
        });

        $this->renderable(function (InvalidSignatureException $e) {
            return Inertia::render('Errors/LoginRequiredPage', [
                'code' => 413,
                'help' => 'This Page has Expired. Please try again by logging in',
                'error' => 'Signature Invalid',
            ]);
        });

        $this->renderable(function (HttpException $e, $request) {
            if ($e->getStatusCode() == 403) {
                return Inertia::render('Errors/NotFoundPage', [
                    'code' => 403,
                    'error' => 'Page Forbidden',
                    'help' => 'Access denied. The page you are looking for might be temporarily unavailable.',
                ])->toResponse($request)->setStatusCode(403);
            }
        });

        $this->renderable(function (AccessDeniedHttpException $e, $request) {
            return Inertia::render('Errors/NotFoundPage', [
                'code' => 403,
                'error' => 'Page Forbidden',
                'help' => 'Access denied. The page you are looking for might be temporarily unavailable.',
            ])->toResponse($request)->setStatusCode(403);
        });

        $this->renderable(function (NotFoundHttpException $e, $request) {
            return Inertia::render('Errors/NotFoundPage', [
                'code' => 404,
                'error' => 'Page Not Found',
                'help' => 'The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.',
            ])->toResponse($request)->setStatusCode(404);
        });

        $this->renderable(function (\Illuminate\Http\Exceptions\ThrottleRequestsException $e, $request) {
            return Inertia::render('Errors/BackCustomMessage', [
                'code' => 429,
                'help' => 'You have made too many requests. Please wait a while before trying again.',
                'error' => 'Too Many Requests',
            ]);
        });

        // $this->renderable(function (Throwable $e, $request) {
        //     return Inertia::render('Errors/BackCustomMessage', [
        //         'code' => 500,
        //         'error' => 'Internal Server Error',
        //         'help' => 'Something went wrong on our end. Please try again later or contact support if the issue persists.',
        //     ]);
        // });

        // MethodNotAllowedHttpException
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function report(Throwable $exception)
    {
        if (
            $exception instanceof \Illuminate\Validation\ValidationException ||
            request()->isPrecognitive()
        ) return;

        $errorBody = [
            'error' => get_class($exception),
            'message' => $exception->getMessage(),
            'url' => Request::url(),
            'code' => $exception->getCode(),
            'ip' => ClientIp::getClientIp(request()),
            'method' => request()->method(),
            'user_id' => request()->user() ? request()->user()->id : 'guest',
            'trace' => $exception->getTraceAsString(),
        ];

        app(AuthLogger::class)->error(json_encode($errorBody));
    }
}
