<?php

namespace App\Modules\DomainCancellationRequest\Services;

use App\Mail\DomainDeletionNotice;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainCancellationEmailService {

    public static function instance(): self
    {
        $DomainCancellationEmailService = new self;

        return $DomainCancellationEmailService;
    }

    public static function sendMail($requestCancellation) {
        // dd($requestCancellation);
        Mail::to(Auth::user()->email)->queue(new DomainDeletionNotice($requestCancellation));
    }

}