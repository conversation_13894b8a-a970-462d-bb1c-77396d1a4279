<?php

namespace App\Modules\Push\Requests;

use App\Modules\Push\Services\OutgoingPushDomainService;
use App\Modules\TransactionThreshold\Services\TransactionThresholdService;
use App\Rules\CannotTransferToSelf;
use App\Rules\EmailConfirmed;
use App\Rules\EmailDoesNotExist;
use App\Rules\PushDomainRecipientExists;
use App\Util\Constant\Transaction;
use Illuminate\Foundation\Http\FormRequest;

class OutgoingPushSendDomainRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $registeredDomainIds = array_column($this->domains, 'registered_domain_id');

        return [
            'domains' => ['array'],
            'domains.registered_domain_id' => ['array'],
            'domains.registered_domain_id.*' => ['integer', 'exists:registered_domains,id'],
            'email' => [
                'required',
                'email:rfc,dns',
                new CannotTransferToSelf,
                new EmailDoesNotExist,
                new PushDomainRecipientExists($registeredDomainIds),
            ],
            'email_confirmation' => ['required', 'email:rfc,dns', new EmailConfirmed($this->email)],
        ];
    }

    protected function passedValidation()
    {
        $this->merge(['domains' => array_column($this->domains, 'registered_domain_id')]);

        $count = count($this->domains);
        TransactionThresholdService::instance()->validateTransaction(Transaction::PUSH, $count);
    }

    public function send(): void
    {
        OutgoingPushDomainService::instance()->send($this->domains, $this->email);
    }

    public function authenticate(): void
    {
        OutgoingPushDomainService::instance()->authenticateUser($this->password);
    }
}
