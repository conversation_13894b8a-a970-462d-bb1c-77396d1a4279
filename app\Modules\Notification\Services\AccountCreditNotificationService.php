<?php

namespace App\Modules\Notification\Services;

use App\Events\NotificationEvent;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Notification\Constants\NotificationType;

class AccountCreditNotificationService extends NotificationService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $accountCreditNotificationService = new self;

        return $accountCreditNotificationService;
    }

    public function sendInvalidAccountCreditBalanceNotif($notifData): void
    {
        $title = 'Invalid Account Credit Balance';
        $message = 'You failed to deposit invalid amount of $"'.$notifData['amount'].'" with previous balance of $"'.$notifData['balance'].'" to Account Credit.';
        $notification = NotificationHandler::For($notifData['user_id'])->addPayload($title, $message, 'account.balance.index', NotificationType::CRITICAL);
        $notification->store();
        event(new NotificationEvent($notifData['user_id']));
    }

    public function sendAccountDebitSuccessNotif($notifData)
    {
        $title = 'Funds Added to Your Account';
        $message = '$'.$notifData['amount'].' has been added to your account. Your new balance is $'.$notifData['balance'].'.';
        $notification = NotificationHandler::For($notifData['user_id'])->addPayload($title, $message, 'account.balance.index', NotificationType::MEDIUM);
        $notification->store();
        event(new NotificationEvent($notifData['user_id']));
    }

    public function sendBankTransferApprovedNotif($notifData): void
    {
        $title = 'Wire Transfer Approved';
        $message = 'Your wire transfer request for $'.$notifData['amount'].' has been approved and will be processed shortly.';
        $notification = NotificationHandler::For($notifData['user_id'])->addPayload($title, $message, 'wire.transfer.index', NotificationType::MEDIUM);
        $notification->store();
        event(new NotificationEvent($notifData['user_id']));
    }

    public function sendBankTransferRejectedNotif($notifData): void
    {
        $title = 'Wire Transfer Rejected';
        $message = 'Your wire transfer request for $'.$notifData['amount'].' has been rejected. Reason: '.$notifData['reason'].'.';
        $notification = NotificationHandler::For($notifData['user_id'])->addPayload($title, $message, 'wire.transfer.index', NotificationType::CRITICAL);
        $notification->store();
        event(new NotificationEvent($notifData['user_id']));
    }
}
