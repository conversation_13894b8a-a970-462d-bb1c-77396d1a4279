<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserPaymentMethod extends Model
{
    /**
     * Manually Define Table Name.
     *
     * @var string
     */
    protected $table = 'user_payment_methods';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable =
    [
        'user_id',
        'payment_method_id',
        'card_nickname',
        'is_default',
        'is_disabled',
        'disabled_at',
        'failed_attempts',
        'last_failure_at',
        'failure_reason'
    ];

    /**
     * Define attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden =
    [
        //...
    ];

    /**
     * Cast Type
     *
     * @var array
     */
    protected $casts =
    [
        'is_default' => 'boolean',
        'is_disabled' => 'boolean',
        'disabled_at' => 'datetime',
        'last_failure_at' => 'datetime',
        'failed_attempts' => 'integer'
    ];
}
