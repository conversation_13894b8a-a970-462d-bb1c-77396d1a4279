<?php

namespace App\Listeners;

use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\DB;

class AddUserTransaction
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $transactionIds = DB::table('transactions')->pluck('id')->toArray();
        $payload = [];
        $now = Carbon::now();

        foreach ($transactionIds as $transactionId) {
            $payload[] = [
                'user_id' => $event->user->id,
                'transaction_id' => $transactionId,
                'custom_limit' => 0,
                'created_at' => $now,
                'updated_at' => $now
            ];
        }

        DB::table('user_transactions')->insert($payload);
    }
}
