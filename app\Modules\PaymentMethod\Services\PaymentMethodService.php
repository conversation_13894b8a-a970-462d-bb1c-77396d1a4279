<?php

namespace App\Modules\PaymentMethod\Services;

use App\Models\User;
use App\Models\UserPaymentMethod;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Providers\StripeKeyProvider;
use Exception;
use Illuminate\Support\Facades\Auth;
use Stripe\PaymentMethod;
use Stripe\Stripe;

class PaymentMethodService
{
    /**
     * Class Constructor
     *
     * @return void
     */
    public function __construct()
    {
        // ...
    }

    /**
     * Get List Data
     */
    public function getListData(string $userId): array
    {
        $user = User::findOrFail($userId);

        $items = [];
        $defaultItem = null;

        if ($user->stripe_customer_id != null) {
            $items = $this->fetchPaymentMethods($user->stripe_customer_id);
            $defaultItem = $this->fetchDefaultPaymentMethod($user->id);
            $primaryPaymentMethod = $this->fetchPrimaryPaymentMethod($user->id);
        }

        $publicKey = (new StripeKeyProvider)->getPromise();

        return compact('items', 'defaultItem', 'publicKey', 'primaryPaymentMethod');
    }

    /**
     * Get User Payment Methods
     */
    public function getUserPaymentMethods(string $userId)
    {
        $user = User::findOrFail($userId);

        $items = [];
        $defaultItem = null;

        if ($user->stripe_customer_id != null) {
            $items = $this->fetchPaymentMethods($user->stripe_customer_id);
            $defaultItem = $this->fetchDefaultPaymentMethod($user->id);
        }

        return json_encode(
            [
                'items' => $items,
                'default' => $defaultItem,
            ]
        );
    }

    /**
     * Get User Payment Methods
     */
    public function processSetupIntent(string $userId)
    {
        $user = User::findOrFail($userId);

        $customerId = $user->stripe_customer_id;

        if ($customerId == null) {
            $customer = $this->setupCustomer($user->id);
            $customerId = $customer['id'];
        }

        return $this->createSetupIntent(
            $customerId,
        );
    }

    /**
     * Fetch Payment Methods
     *
     *
     * @return array
     */
    public function fetchPaymentMethods(?string $customerId)
    {
        if ($customerId == null) {
            return [];
        }

        Stripe::setApiKey((new StripeKeyProvider)->getSecretKey());

        try {
            // List the payment methods associated with the customer
            $paymentMethods = PaymentMethod::all(
                [
                    'customer' => $customerId,
                    'type' => 'card',
                ]
            );

            return $paymentMethods->data;
        } catch (Exception $e) {
            app(AuthLogger::class)->error($e->getMessage());

            throw new Exception('Could not load payment methods');
        }
    }

    /**
     * Fetch Default Payment Method
     */
    public function fetchDefaultPaymentMethod(string $userId)
    {
        $paymentMethod = UserPaymentMethod::where('user_id', '=', $userId)
            ->where('is_default', '=', true)
            ->first();

        if ($paymentMethod == null) {
            return null;
        }

        return $paymentMethod;
    }

    /**
     * Fetch Payment Method
     *
     * @param  string  $paymentMethodId
     *
     *  //@return object
     */
    public function fetchPaymentMethod(string $paymentMethodId) // : object
    {
        Stripe::setApiKey((new StripeKeyProvider)->getSecretKey());

        try {
            $paymentMethod = PaymentMethod::retrieve(
                $paymentMethodId
            );

            return $paymentMethod;
        } catch (\Exception $e) {
            // ...
        }
    }

    public function fetchPrimaryPaymentMethod(int $id)
    {
        try {
            return User::findOrFail($id)->primary_payment_method;
        } catch (\Exception $e) {
            //throw $th;
        }
    }

    /**
     * Store Payment Method
     *
     *
     * @return void
     */
    public function storePaymentMethod(array $data, string $userId)
    {
        // Check if this is the user's first payment method
        $existingPaymentMethodsCount = UserPaymentMethod::where('user_id', $userId)->count();
        $isFirstPaymentMethod = $existingPaymentMethodsCount === 0;

        UserPaymentMethod::create(
            [
                'user_id' => $userId,
                'payment_method_id' => $data['paymentMethodId'],
                'card_nickname' => $data['cardNickname'],
                'is_default' => $isFirstPaymentMethod, // Automatically set as default if it's the first payment method
            ]
        );

        // Log this action for debugging
        if ($isFirstPaymentMethod) {
            app(\App\Modules\CustomLogger\Services\AuthLogger::class)->info(
                "PaymentMethodService: First payment method {$data['paymentMethodId']} automatically set as default for user {$userId}"
            );
        }
    }

    /**
     * Remove Payment Method
     *
     *
     * @return void
     */
    public function removePaymentMethod(string $paymentMethodId)
    {
        Stripe::setApiKey((new StripeKeyProvider)->getSecretKey());

        try {
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);

            $paymentMethod->detach();

            UserPaymentMethod::where('payment_method_id', '=', $paymentMethodId)->delete();

            return response()->json(
                [
                    'message' => 'Payment method removed successfully',
                    'payment_service_type' => $paymentMethod,
                ]
            );
        } catch (\Exception $e) {
            return response()->json(
                [
                    'error' => $e->getMessage(),
                ],
                400
            );
        }
    }

    /**
     * Setup Customer
     *
     *
     * @return array
     */
    public function setupCustomer(int $userId)
    {
        $user = User::findOrFail($userId);

        $stripe = (new StripeKeyProvider)->getStripeClient();

        $customer = $stripe->customers->create(
            [
                'name' => "{$user->first_name} {$user->last_name}",
                'email' => $user->email,
            ]
        );

        $user->stripe_customer_id = $customer->id;
        $user->save();

        return $customer;
    }

    /**
     * Set Payment as Default
     */
    public function setPaymentAsDefault(array $data, int $userId): void
    {
        $paymentMethod = UserPaymentMethod::where(
            [
                'payment_method_id' => $data['paymentMethodId'],
                'card_nickname' => $data['cardName'],
                'user_id' => $userId,
            ]
        )
            ->firstOrFail();

        $paymentMethod->is_default = true;
        $paymentMethod->save();

        UserPaymentMethod::where('id', '!=', $paymentMethod->id)
            ->where('user_id', '=', $userId)
            ->update(
                [
                    'is_default' => false,
                ]
            );
    }

    /**
     * Add Payment Method
     *
     *
     * @return void
     */
    public function createSetupIntent(string $customerId): bool|string
    {
        $stripe = (new StripeKeyProvider)->getStripeIntentClient();

        $intent = $stripe->setupIntents->create(
            [
                'customer' => $customerId,
                'automatic_payment_methods' => ['enabled' => true],
            ]
        );

        return json_encode(['clientSecret' => $intent->client_secret]);
    }

    /**
     * Create Payment Intent
     */
    public function createPaymentIntent(array $data, ?string $customerId)
    {
        $payload =
            [
                'amount' => $data['amount'],
                'currency' => 'usd',
                'payment_method' => $data['paymentMethodId'],
            ];

        if (isset($customerId)) {
            $payload['customer'] = $customerId;
        }

        $paymentIntent = PaymentIntentProvider::instance()->create($payload);

        return json_encode(
            [
                'clientSecret' => $paymentIntent->client_secret,
                'paymentIntentId' => $paymentIntent->id,
            ]
        );
    }
    public function updateUserPrimaryPaymentMethod($id, $request)
    {
        User::findOrFail($id)->update(['primary_payment_method' => $request->input('primary_payment_method')]);
        if(!($request->input('primary_payment_method') == 'saved_card')) {
            $paymentMethod = UserPaymentMethod::where('user_id', Auth::user()->id);
            $paymentMethod->update(['is_default'=>false]);
        }
    }

    public function fetchDefaultCard($id)
    {
        $defaultCard = UserPaymentMethod::where('user_id', $id)->where('is_default', true)->get();
        return $defaultCard;
    }
}
